<script setup>
import {
  onMounted,
  ref,
  onActivated,
  nextTick,
  onUnmounted,
  onDeactivated
} from "vue";
import { draftFindAll, draftDelete } from "@/api/drafts.js";

import { formatTime } from "@/utils/index";
import { requestTo } from "@/utils/http/tool";
import { to } from "@iceywu/utils";
import { ElMessage, ElMessageBox } from "element-plus";
import { useRouter } from "vue-router";
defineOptions({
  name: "Drafts"
});
onMounted(() => {
  getTableList();
  calculateTableHeight();
  window.addEventListener("resize", calculateTableHeight);
});
onActivated(() => {
  getTableList();
  calculateTableHeight();
  window.addEventListener("resize", calculateTableHeight);
});
onUnmounted(() => {
  window.removeEventListener("resize", calculateTableHeight);
});

onDeactivated(() => {
  window.removeEventListener("resize", calculateTableHeight);
});
// 计算表格高度
const searchFormHeight = ref(0);
const tableHeight = ref("100vh-230px");
const calculateTableHeight = async () => {
  await nextTick();
  const searchForm = document.querySelector(".search .con_search");
  if (searchForm) {
    searchFormHeight.value = searchForm.offsetHeight;
    // 动态计算表格高度，减去搜索框高度、表头按钮高度、分页器高度和其他间距
    tableHeight.value = `calc(100vh - 230px - ${searchFormHeight.value}px)`;
  }
};

// 数据转换方法
function convertDataToTableData(sourceData) {
  // 获取所有的 draftFlowType 键
  const allDraftFlowTypes = draftFlowType.map(item => Object.keys(item)[0]);

  return sourceData.map(item => {
    // 创建一个映射，存储原始数据中已存在的 draftFlow
    const existingFlowsMap = {};
    item.draftFlows.forEach(flow => {
      existingFlowsMap[flow.draftFlowType] = flow;
    });

    // 为每个 draftFlowType 创建对应的 draftFlow
    const completeDraftFlows = allDraftFlowTypes.map(flowType => {
      if (existingFlowsMap[flowType]) {
        // 如果原数据中存在，使用原数据
        return {
          ...existingFlowsMap[flowType],
          // 确保字符串格式
          createdAt: String(existingFlowsMap[flowType].createdAt),
          updatedAt: String(existingFlowsMap[flowType].updatedAt)
        };
      } else {
        // 如果原数据中不存在，创建默认数据
        return {
          id: item.id,
          createdAt: String(item.createdAt),
          updatedAt: String(item.updatedAt),
          draftFlowType: flowType,
          completed: false
        };
      }
    });

    return {
      id: String(item.id),
      createdAt: String(item.createdAt),
      updatedAt: String(item.updatedAt),
      saveTime: item.saveTime,
      courseName: item.courseName,
      coursePeriodName: item.coursePeriodName,
      courseId: item.courseId ? String(item.courseId) : null,
      draftFlows: completeDraftFlows
    };
  });
}

const draftFlowType = [
  { BASIC_INFORMATION: "基础信息" },
  { COURSE_ITINERARY: "课期行程" },
  { COURSE_INTRODUCTION: "课期介绍" },
  { COURSE_KNOWLEDGE_POINT: "课期知识点" },
  { ASSIGNMENT_DESIGN: "作业设计" },
  { EQUIPMENT_DESCRIPTION: "材料说明" },
  { PRECAUTIONS: "注意事项" },
  { USER_AGREEMENT: "用户协议" },
  { PRICE_SETTING: "价格设置" }
];

const draftFlowTypeName = {
  BASIC_INFORMATION: "基础信息",
  COURSE_ITINERARY: "课期行程",
  COURSE_INTRODUCTION: "课期介绍",
  COURSE_KNOWLEDGE_POINT: "课期知识点",
  ASSIGNMENT_DESIGN: "作业设计",
  EQUIPMENT_DESCRIPTION: "材料说明",
  PRECAUTIONS: "注意事项",
  USER_AGREEMENT: "用户协议",
  PRICE_SETTING: "价格设置"
};

const router = useRouter();
// 搜索表单
const form = ref({
  startTime: "",
  endTime: "",
  courseName: "",
  coursePeriodName: ""
});
// 表格数据
const tableData = ref([]);
const params = ref({
  page: 1,
  size: 10,
  sort: "createdAt,desc",
  totalElements: 0
});

// 获取列表信息
const getListLoading = ref(false);
const getTableList = async data => {
  if (getListLoading.value) {
    return;
  }
  getListLoading.value = true;
  let paramsData = {
    page: params.value.page - 1,
    size: params.value.size,
    sort: "saveTime,desc"
  };

  for (const paramsDataKey in form.value) {
    let isArray = Array.isArray(form.value[paramsDataKey]);
    if (isArray) {
      if (form.value[paramsDataKey].length > 0) {
        paramsData[paramsDataKey] = form.value[paramsDataKey];
      }
    } else {
      if (form.value[paramsDataKey]) {
        paramsData[paramsDataKey] = form.value[paramsDataKey];
      }
    }
  }
  if (paramsData.freeze && form.value.freeze === 1) {
    paramsData.freeze = false;
  } else if (paramsData.freeze && form.value.freeze === 2) {
    paramsData.freeze = true;
  }
  const [err, result] = await requestTo(draftFindAll(paramsData));
  if (result) {
    tableData.value = convertDataToTableData(result?.content);
    params.value.totalElements = result.totalElements;
  } else {
    ElMessage.error(err);
  }
  getListLoading.value = false;
};

//分页
const handleCurrentChange = e => {
  params.value.page = e;
  getTableList();
};
//搜索
const searchData = () => {
  params.value.page = 1;
  getTableList();
};
// 重置
const setData = () => {
  form.value = {};
  params.value.page = 1;
  value1.value = [];
  getTableList();
};
// 清除数据
const clearEvt = val => {
  if (val === "time") {
    form.value.startTime = "";
    form.value.endTime = "";
  } else if (val === "courseName") {
    form.value.courseName = "";
  } else if (val === "coursePeriodName") {
    form.value.coursePeriodName = "";
  }
  getTableList();
};

const value1 = ref([]);
// 选择时间
const timeChange = value => {
  if (value) {
    form.value.startTime = new Date(value[0])?.getTime();
    const endDate = new Date(value[1]);
    endDate.setHours(23, 59, 59, 999); // 关键修改
    form.value.endTime = endDate.getTime();
  }
};

// 删除
const deleteEvt = val => {
  ElMessageBox.confirm(`确定要删除该课程吗？`, "删除课程", {
    confirmButtonText: "确定",
    cancelButtonText: "取消"
  })
    .then(async () => {
      const operateLog = {
        operateLogType: "COURSE_MANAGEMENT",
        operateType: `删除了草稿箱中的“${val.courseName}”课程`
        // operatorTarget: form.value.name,
      };
      let [err, res] = await to(draftDelete({ id: val.id }, operateLog));
      if (res.code === 200) {
        ElMessage.success("删除成功");
        getTableList();
      } else {
        ElMessage.error(`删除失败,${res.msg}`);
      }
      if (err) {
        ElMessage.error("删除失败");
      }
    })
    .catch(() => {});
};
</script>

<template>
  <div class="containers">
    <div class="search">
      <div class="con_search">
        <el-form :model="form" :inline="true">
          <el-form-item label="保存时间">
            <el-date-picker
              v-model.trim="value1"
              type="daterange"
              start-placeholder="请选择开始时间"
              end-placeholder="请选择结束时间"
              @change="timeChange"
              @clear="clearEvt('time')"
            />
          </el-form-item>
          <el-form-item label="课程名">
            <el-input
              v-model.trim="form.courseName"
              placeholder="请输入"
              clearable
              @clear="clearEvt('courseName')"
            />
          </el-form-item>
          <el-form-item label="课期名">
            <el-input
              v-model.trim="form.coursePeriodName"
              placeholder="请输入"
              clearable
              @clear="clearEvt('coursePeriodName')"
            />
          </el-form-item>
          <el-form-item label=" ">
            <div class="flex">
              <el-button type="primary" @click="searchData">搜索</el-button>
              <el-button @click="setData">重置</el-button>
            </div>
          </el-form-item>
        </el-form>
      </div>
    </div>
    <div class="main">
      <el-scrollbar :style="{ height: tableHeight }">
        <div class="con_table">
          <el-table
            :data="tableData"
            :header-cell-style="{
              backgroundColor: '#fafafa',
              color: '#565353'
            }"
            table-layout="fixed"
            :max-height="tableHeight"
          >
            <el-table-column
              prop="createdAt"
              label="保存时间"
              align="left"
              min-width="200"
            >
              <template #default="scope">
                <div>
                  {{
                    formatTime(scope.row?.saveTime, "YYYY-MM-DD HH:mm:ss") ||
                    "--"
                  }}
                </div>
              </template>
            </el-table-column>
            <el-table-column
              prop="name"
              label="课程名"
              show-overflow-tooltip
              align="left"
              width="300"
            >
              <template #default="scope">
                <div>{{ scope.row.courseName || "--" }}</div>
              </template>
            </el-table-column>
            <el-table-column
              prop="name"
              label="课期名"
              show-overflow-tooltip
              align="left"
              width="300"
            >
              <template #default="scope">
                <div>{{ scope.row.coursePeriodName || "--" }}</div>
              </template>
            </el-table-column>
            <el-table-column
              prop="courseTypeName"
              label="完成度"
              align="left"
              min-width="250"
            >
              <template #default="{ row }">
                <div class="flex">
                  <el-tooltip
                    v-for="(it, ind) in row.draftFlows"
                    :key="ind"
                    class="box-item"
                    effect="dark"
                    :content="draftFlowTypeName[it.draftFlowType]"
                  >
                    <div
                      class="box"
                      :class="it.completed ? 'active' : 'noactive'"
                    />
                  </el-tooltip>
                </div>
              </template>
            </el-table-column>

            <el-table-column
              fixed="right"
              label="操作"
              align="left"
              width="200"
            >
              <template #default="scope">
                <div class="option">
                  <div
                    class="btnse btnseVE"
                    @click="
                      router.push({
                        path: '/course/courseCreate',
                        query: {
                          draftId: scope.row.id,
                          type: 'draft',
                          courseId: scope.row.courseId
                        }
                      })
                    "
                  >
                    继续编辑
                  </div>
                  <div
                    class="btnse"
                    style="color: #f56c6c"
                    @click="deleteEvt(scope.row)"
                  >
                    删除
                  </div>
                </div>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </el-scrollbar>
      <div class="con_pagination">
        <!-- 分页 -->
        <el-pagination
          v-model:current-page="params.page"
          v-model:page-size="params.size"
          class="pagination"
          background
          layout="total, prev, pager, next, jumper"
          :total="params.totalElements"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.box {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  margin-right: 4px;
}

.active {
  background-color: #41d289;
}

.noactive {
  background-color: #bbbbbb;
}

.scrollbar {
  height: calc(100vh - 335px);
  background-color: #fff;
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow-y: hidden;
}
:deep(.el-table .cell) {
  padding: 0;
}
.containers {
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  .search {
    padding: 20px 20px 2px 20px;
    background-color: #fff;
    margin-bottom: 20px;
    .con_search {
      display: flex;
      align-items: center;
      width: 100%;
      height: fit-content;
      overflow-y: auto;
      // .input_width {
      //   width: 200px;
      // }
    }
  }

  .main {
    background: #fff;
    padding: 20px;
    box-sizing: border-box;
    // height: 100%;
    // flex: 1;
    display: flex;
    flex-direction: column;
    overflow-y: hidden;
    .btns {
      width: 100%;
      display: flex;
      justify-content: flex-end;
      margin-bottom: 20px;
    }
  }
  .con_table {
    flex: 1;
    display: flex;
    flex-direction: column;
    .option {
      display: flex;
      .btnse {
        color: #409eff;
        cursor: pointer;
        margin-right: 20px;
      }
      .btnseVE {
        margin-left: 10px;
      }
    }
  }

  .con_pagination {
    display: flex;
    justify-content: flex-end;
    width: 100%;
    margin-top: 20px;
  }
}
</style>
