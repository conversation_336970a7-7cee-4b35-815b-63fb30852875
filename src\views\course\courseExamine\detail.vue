<script setup>
import { ref, onMounted } from "vue";
import { useRoute } from "vue-router";
import TabTitle from "@/components/Base/tabInfo.vue";
import BaseInfo from "./components/baseInfo.vue";
import Scheduling from "@/components/course/scheduling.vue";
import PriceSetting from "@/components/course/priceSetting.vue";
import courseIntroduction from "@/components/course/courseIntroduction.vue";
import JobDesign from "@/components/course/jobDesign.vue";
import {
  findByApplyId,
  reviewApproval,
  findcoursePeriodId,
  findBasicInformation,
  bureauFindBasicInformation
} from "@/api/course.js";
import { requestTo } from "@/utils/http/tool";
import { ElMessage, ElMessageBox } from "element-plus";
import { APPLY_STATE, APPROVAL_TYPE } from "@/utils/enum";
import { to } from "@iceywu/utils";
import { useUserStoreHook } from "@/store/modules/user";
import dayjs from "dayjs";
import { ImageThumbnail } from "@/utils/imageProxy.js";
import { localEndApplyReview, localEndFindByApplyId } from "@/api/localEnd.js";
const route = useRoute();
const textarea = ref("");
const infoShow = ref(true);
const tableShow = ref("基础信息");
// 表头
const tableHeader = ref([
  {
    id: "1",
    label: "课程名",
    value: "--",
    width: "107px"
  },
  {
    id: "2",
    label: "期数",
    value: "--",
    width: "107px"
  },
  {
    id: "3",
    label: "机构",
    value: "--",
    width: "107px"
  },
  {
    id: "4",
    label: "申请时间",
    value: "--",
    width: "107px"
  },
  {
    id: "5",
    label: "审核状态",
    value: "--",
    width: "107px"
  },
  {
    id: "6",
    label: "审核类型",
    value: "--",
    width: "107px"
  }
]);
const tableHeaderDetail = ref([
  {
    id: "1",
    label: "课程名",
    value: "--",
    width: "107px"
  },
  {
    id: "2",
    label: "期数",
    value: "--",
    width: "107px"
  },
  {
    id: "3",
    label: "机构",
    value: "--",
    width: "107px"
  },
  {
    id: "4",
    label: "申请时间",
    value: "--",
    width: "107px"
  },
  {
    id: "5",
    label: "审核状态",
    value: "--",
    width: "107px"
  },
  {
    id: "6",
    label: "审核类型",
    value: "--",
    width: "107px"
  },
  {
    id: "7",
    label: "审核员",
    value: "--",
    width: "107px"
  },
  {
    id: "8",
    label: "课程分类",
    value: "--",
    width: "107px"
  },
  {
    id: "9",
    label: "审核意见",
    value: "--",
    width: "107px"
  }
]);
const tabTitle = ref([
  { id: 1, name: "基础信息" },
  { id: 2, name: "行程安排" },
  { id: 3, name: "课期介绍" },
  { id: 4, name: "课期知识点" },
  { id: 5, name: "装备说明" },
  { id: 6, name: "注意事项" },
  { id: 7, name: "价格设置" },
  { id: 8, name: "作业设计" },
  { id: 9, name: "用户协议" }
]);
const baseInfo = ref([
  {
    id: "1",
    label: "课期ID",
    value: "--",
    width: "107px"
  },
  {
    id: "2",
    label: "创建时间",
    value: "--",
    width: "107px"
  },
  {
    id: "3",
    label: "人数上限",
    value: "--",
    width: "107px"
  },
  {
    id: "4",
    label: "开课时间",
    value: "--",
    width: "107px"
  },
  {
    id: "5",
    label: "基地",
    value: "--",
    width: "107px"
  },

  {
    id: "6",
    label: "领队",
    value: "--",
    width: "107px"
  },
  {
    id: "7",
    label: "讲师",
    value: "--",
    width: "107px"
  }
  // {
  //   id: "8",
  //   label: "讲师",
  //   value: "--",
  //   width: "107px"
  // }

  // 其他表头数据项
]);
const noLoading = ref(false);
const passLoading = ref(false);
const tabInfoEvt = obj => {
  // console.log("💗tabInfoEvt---------->", obj);
  tableShow.value = obj.name;
};
// 不通过
const nopassEvt = text => {
  noLoading.value = true;
  ElMessageBox.confirm(`确定要驳回该申请吗？`, "确定驳回", {
    confirmButtonText: "确定",
    cancelButtonText: "取消"
  })
    .then(() => {
      getReviewApproval(text);
      // noLoading.value = false;
    })
    .catch(() => {});
  noLoading.value = false;
};

// 通过
const passEvt = text => {
  passLoading.value = true;
  ElMessageBox.confirm(`确定要通过该申请吗？`, "确定通过", {
    confirmButtonText: "确定",
    cancelButtonText: "取消"
  })
    .then(() => {
      getReviewApproval(text);
      // noLoading.value = false;
    })
    .catch(() => {});
  passLoading.value = false;
};
// 审核
const getReviewApproval = async text => {
  const paramsArg = {
    applyId: Number(route.query.id),
    // opinion:textarea.value,
    auditState: text,
    userType:
      useUserStoreHook().roleTarget === "局端管理员"
        ? "EDUCATION_BUREAU"
        : "PLATFORM_ADMIN"
  };
  if (textarea.value) {
    paramsArg.opinion = textarea.value;
  }
  const operateLog = {
    operateLogType: "COURSE_MANAGEMENT",
    operateType:
      text === "REJECTED"
        ? `驳回了“${tableHeader.value[0].value}”课期的${tableHeader.value[5].value}申请`
        : `通过了“${tableHeader.value[0].value}”课期的${tableHeader.value[5].value}申请`
  };
  // console.log("🐬--paramsArg---------------------------->", paramsArg);
  let api =
    useUserStoreHook().roleTarget === "局端管理员"
      ? localEndApplyReview
      : reviewApproval;
  const [err, res] = await to(api(paramsArg, operateLog));
  if (res.code === 200) {
    ElMessage.success("审核成功");
    infoShow.value = !infoShow.value;
    getFindByApplyId();
  } else {
    ElMessage.error(`审核失败,${res.msg}`);
  }
  if (err) {
    ElMessage.error("审核失败");
  }
};
const params = ref({
  page: 1,
  size: 10,
  sort: "createdAt,desc",
  totalElements: 0
});
// 查询详情
let tableImg = ref("");
const getFindByApplyId = async () => {
  const paramsArg = {
    applyId: route.query.id
  };
  const api =
    useUserStoreHook().roleTarget === "局端管理员"
      ? localEndFindByApplyId
      : findByApplyId;
  const [err, res] = await requestTo(api(paramsArg));
  if (res) {
    // console.log("🐬res------------------------------>", res);
    if (res.auditState === "PENDING_REVIEW") {
      infoShow.value = false;
      tableHeader.value[0].value = res.coursePeriodName || "--";
      tableHeader.value[1].value = res.termNumber || "--";
      tableHeader.value[2].value = res.organizationName || "--";
      tableHeader.value[3].value = res.createdAt
        ? dayjs(res.createdAt).format("YYYY-MM-DD HH:mm:ss")
        : "--";
      tableHeader.value[4].value = APPLY_STATE[res.auditState]?.text || "--";
      tableHeader.value[5].value = APPROVAL_TYPE[res.applyType]?.text || "--";
    } else {
      infoShow.value = true;
      tableHeaderDetail.value[0].value = res.coursePeriodName || "--";
      tableHeaderDetail.value[1].value = res.termNumber || "--";
      tableHeaderDetail.value[2].value = res.organizationName || "--";
      tableHeaderDetail.value[3].value = res.createdAt
        ? dayjs(res.createdAt).format("YYYY-MM-DD HH:mm:ss")
        : "--";
      tableHeaderDetail.value[4].value =
        APPLY_STATE[res.auditState]?.text || "--";
      tableHeaderDetail.value[5].value =
        APPROVAL_TYPE[res.applyType]?.text || "--";
      tableHeaderDetail.value[6].value = res.auditorName || "--";
      tableImg.value = res.files ? res.files[0]?.uploadFile?.url : "";
      tableHeaderDetail.value[7].value = res?.courseType || "--";
      tableHeaderDetail.value[8].value = res.opinion || "--";
    }

    // console.log('🌳 tableData.value------------------------------>',tableData.value);
  }
  if (err) {
    console.log("err------------------------------>", err);
  }
};
// 查询基础信息
const getCoursePeriodId = async () => {
  const paramsArg = {
    coursePeriodId: route.query.periodId
  };
  let api =
    useUserStoreHook().roleTarget === "局端管理员"
      ? bureauFindBasicInformation
      : findBasicInformation;
  const [err, res] = await requestTo(api(paramsArg));
  if (res) {
    // console.log("🐬res---------------3333---3333222------------>", res);
    baseInfo.value[0].value = res?.id || "--";
    baseInfo.value[1].value = res.createdAt
      ? dayjs(res.createdAt).format("YYYY-MM-DD HH:mm:ss")
      : "--";
    baseInfo.value[2].value = res?.maxPeopleNumber || "--";
    baseInfo.value[3].value = res.openTime
      ? dayjs(res.openTime).format("YYYY-MM-DD HH:mm:ss")
      : "--";
    baseInfo.value[4].value = res?.complex?.name || 0;
    baseInfo.value[5].value =
      res?.leaders?.map(it => it.name).join("、") || "--";
    baseInfo.value[6].value =
      res?.lecturers?.map(it => it.name).join("、") || "--";

    // console.log('🌳 tableData.value------------------------------>',tableData.value);
  }
  if (err) {
    console.log("err------------------------------>", err);
  }
};
onMounted(() => {
  getFindByApplyId();
  getCoursePeriodId();
});
</script>

<template>
  <div class="examine-detail">
    <div class="curse-table">
      <!-- 详情 -->
      <div v-if="infoShow" class="examine-info">
        <el-descriptions class="margin-top" title="" :column="3" border>
          <el-descriptions-item
            :rowspan="4"
            :width="140"
            label="课程封面"
            align="center"
          >
            <el-image v-if="tableImg" :src="ImageThumbnail(tableImg)" />
            <span v-else>暂无封面</span>
          </el-descriptions-item>
          <template v-for="(item, index) in tableHeaderDetail" :key="index">
            <el-descriptions-item
              width="120px"
              label-align="center"
              :span="item.label === '课程分类' ? 3 : ''"
            >
              <template #label>
                <div class="cell-item">{{ item.label }}</div>
              </template>
              <div
                :style="{
                  color:
                    item.value === '已驳回'
                      ? '#FF374C'
                      : item.value === '已通过'
                        ? '#409EFF'
                        : ''
                }"
              >
                {{ item.value }}
              </div>
            </el-descriptions-item>
          </template>
        </el-descriptions>
      </div>
      <!-- 审核中 -->
      <div v-else class="examine-pending">
        <div class="pengding-info">
          <el-descriptions class="margin-top" title="" :column="3" border>
            <template v-for="(item, index) in tableHeader" :key="index">
              <el-descriptions-item width="120px" label-align="center">
                <template #label>
                  <div class="cell-item">{{ item.label }}</div>
                </template>
                <div
                  :style="{ color: item.value === '待审批' ? '#FF9C41' : '' }"
                >
                  {{ item.value }}
                </div>
              </el-descriptions-item>
            </template>
          </el-descriptions>
        </div>
        <div class="opinion">
          <div class="opinion-input">
            <div class="opinion-input-title">审核意见</div>
            <el-input
              v-model.trim="textarea"
              :rows="3"
              type="textarea"
              placeholder="请输入"
              resize="none"
              class="textarea"
            />
          </div>
          <div class="opinion-btn">
            <!-- <div class="pass-btn" @click="passEvt('APPROVED')">通过</div>
            <div class="pass-btn" @click="nopassEvt('REJECTED')">驳回</div> -->
            <el-button
              type="primary"
              style="margin: 30px 0 10px 0"
              :loading="passLoading"
              @click="passEvt('APPROVED')"
            >
              通过
            </el-button>
            <el-button
              type="danger"
              style="margin-left: 0"
              :loading="noLoading"
              @click="nopassEvt('REJECTED')"
            >
              驳回
            </el-button>
          </div>
        </div>
      </div>
    </div>
    <div class="info-table">
      <!-- tab切换 -->
      <TabTitle :tabTitle="tabTitle" @tab-data="tabInfoEvt" />
      <!-- 切换信息 -->
      <div class="tab-info">
        <BaseInfo
          v-if="tableShow === '基础信息'"
          :baseInfo="baseInfo"
          :baseInfoNo="baseInfoNo"
          :type="route.query.type"
        />
        <Scheduling
          v-if="tableShow === '行程安排'"
          :periodId="Number(route.query.periodId)"
        />
        <PriceSetting
          v-if="tableShow === '价格设置'"
          :periodId="Number(route.query.periodId)"
        />
        <courseIntroduction
          v-if="
            tableShow === '课期介绍' ||
            tableShow === '课期知识点' ||
            tableShow === '装备说明' ||
            tableShow === '注意事项' ||
            tableShow === '用户协议'
          "
          :periodId="Number(route.query.periodId)"
          :tableTitle="tableShow"
        />
        <JobDesign
          v-if="tableShow === '作业设计'"
          :periodId="Number(route.query.periodId)"
        />
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.examine-detail {
  // width: calc(100% - 48px);
  // height: calc(100vh - 48px);
  // height: calc(100vh - 150px);
  // overflow-y: auto;
  .curse-table {
    box-sizing: border-box;
    width: 100%;
    padding: 24px 20px;
    // height: 250px;
    margin-bottom: 30px;
    // width: calc(100% - 48px);
    background-color: #fff;

    .pengding-info {
      width: 86%;
      margin-bottom: 18px;
    }

    .opinion {
      display: flex;
      justify-content: space-between;
      width: 100%;

      .opinion-input {
        flex: 1;
        width: 100%;

        .opinion-input-title {
          margin-bottom: 10px;
          font-size: 14px;
        }

        .textarea {
          width: 92%;
        }
      }

      .opinion-btn {
        // display: flex;
        // flex-wrap: wrap;
        // width: 6%;
        // justify-content: space-between;
        display: flex;
        flex-direction: column;
        justify-content: flex-end;
        align-items: center;
      }
    }
  }

  .info-table {
    width: 100%;
    height: 436px;

    .tab-info {
      box-sizing: border-box;
      width: 100%;
      height: 522px;
      padding: 20px 20px;
      background-color: #fff;
      // border-top:2px solid red;
    }
  }
}

:deep(.el-descriptions__label.el-descriptions__cell.is-bordered-label) {
  background: #e1f5ff;
}
</style>
