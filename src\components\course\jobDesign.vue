<script setup>
import { onMounted, ref } from "vue";
import { useRouter, useRoute } from "vue-router";
import { requestTo } from "@/utils/http/tool";
import { ElMessage } from "element-plus";
import {
  assignmentDesignFindAll,
  bureauAssignmentDesignFindAll
} from "@/api/course.js";
import ImgList from "@/components/Base/list1.vue";
import { useUserStoreHook } from "@/store/modules/user";
const router = useRouter();
const route = useRoute();
const evaluateValue = ref("");
const fileListAPi = ref([]);
const showPreview = ref(false);

const srcList = ref([]);
const srcId = ref(0);
// 图片预览
const handleClick = id => {
  showPreview.value = true;
  srcId.value = id;
};
// 查询作业设计
const getDetail = async () => {
  const params = {
    coursePeriodId: Number(route.query.periodId)
  };
  let api =
    useUserStoreHook().roleTarget === "局端管理员"
      ? bureauAssignmentDesignFindAll
      : assignmentDesignFindAll;
  let [err, res] = await requestTo(api(params));
  if (res) {
    evaluateValue.value = res?.content;
    if (res.files?.length) {
      res.files.map(item => {
        fileListAPi.value.push(item?.uploadFile);
        srcList.value.push(item?.uploadFile?.url);
      });
    }
    // console.log("🐳-----res-----", res);
  } else {
    console.log("🐬-----err-----", err);
  }
};
onMounted(() => {
  getDetail();
});
</script>

<template>
  <div class="scheduling">
    <div class="content">
      <el-input
        v-model="evaluateValue"
        :rows="6"
        type="textarea"
        resize="none"
        :disabled="true"
        :placeholder="evaluateValue ? evaluateValue : '暂无数据'"
      />
    </div>
    <div class="work-img">
      <div class="title">作业配图</div>
      <ImgList
        v-if="fileListAPi?.length"
        :imgList="fileListAPi"
        :srcList="srcList"
        :itemCount="fileListAPi?.length"
      />
      <!-- <div v-if="fileListAPi?.length" class="banner">
        <el-carousel
          :interval="4000"
          type="card"
          height="200px"
          :autoplay="false"
        >
          <el-carousel-item v-for="(item, index) in fileListAPi" :key="item">
            <img :src="item.url" class="h-full" @click="handleClick(index)">
          </el-carousel-item>
        </el-carousel>
        <el-image-viewer
          v-if="showPreview"
          :url-list="srcList"
          show-progress
          :initial-index="srcId"
          @close="showPreview = false"
        />
      </div> -->
      <el-empty v-else style="height: 200px" description="暂无配图" />
    </div>
  </div>
</template>

<style lang="scss" scoped>
.scheduling {
  .content {
    margin-bottom: 30px;
  }

  .work-img {
    .title {
      margin-bottom: 6px;
      font-size: 14px;
      color: rgb(16 16 16 / 100%);
    }
  }

  :deep(.el-carousel__item) {
    margin: 0;
    line-height: 200px;
    text-align: center;
  }
}
</style>
