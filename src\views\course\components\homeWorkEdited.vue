<script setup>
import { onMounted, ref } from "vue";
import { useRouter, useRoute } from "vue-router";
import { requestTo } from "@/utils/http/tool";
import { ElMessage, ElMessageBox, ElLoading } from "element-plus";
import { findAssignmentDesign, assignmentDesign } from "@/api/period.js";
import {
  draftAssignmentDesignFindByDraftId,
  saveDraftAssignmentDesign,
  draftDelete
} from "@/api/drafts.js";
import { Plus } from "@element-plus/icons-vue";
import { uploadFile, isOverSizeLimit } from "@/utils/upload/upload.js";
import DescriptionList from "./descriptionList.vue";
import { to } from "@iceywu/utils";
import { aiNewPage } from "@/utils/aiTool.js";
import { courseStore } from "@/store/modules/course.js";
const props = defineProps({
  infoShow: {
    type: String,
    default: ""
  },
  draftId: {
    type: Number,
    default: 0
  },
  periodName: {
    type: String,
    default: "未命名"
  },
  infoShowEnName: {
    type: String,
    default: "foundation"
  }
});
const emites = defineEmits(["baseInfo"]);
const useCourseStore = courseStore();
const router = useRouter();
const route = useRoute();
const evaluateValue = ref("");
const imgText = ref(
  "支持上传png、jpg、jpeg图片格式，最多上传9张，最佳尺寸：750*750px，单张大小不超过10MB"
);
// 查询作业设计
const getDetail = async () => {
  const params = {
    coursePeriodId: Number(route.query.periodId)
  };

  let [err, res] = await requestTo(findAssignmentDesign(params));
  if (res) {
    evaluateValue.value = res?.content;
    if (res.files?.length) {
      res.files.map(item => {
        fileListAPi.value.push(item?.uploadFile);
        fileList.value.push(item?.uploadFile);
      });
    }
    console.log("🐳-----res-----", res);
  } else {
    console.log("🐬-----err-----", err);
  }
};
const fileList = ref([]);
const fileListAPi = ref([]);
// 图片上传
const beforeUpload = async file => {
  let imgType = ["jpg", "jpeg", "png"];
  let fileType = file.type.split("/")[1];
  if (!imgType.includes(fileType)) {
    ElMessage.error("上传图片格式不支持，请上传png，jpg， jpeg格式的图片");
    return false;
  }
  if (fileListAPi.value?.length > 8) {
    ElMessage.error("上传图片数量过多，最多上传9张图片");
    return false;
  }
  try {
    if (file.type.startsWith("image/") === false) {
      ElMessage.error("上传类型不支持");
      fileList.value = fileList.value.filter(() => false);
    }
    const { data, code } = await uploadFile(file, progress => {
      // 构造用于 el-upload 展示的文件对象
      const currentFile = {
        name: file.name,
        uid: file.uid,
        status: progress.status || "uploading",
        percentage: progress.percent || 0
      };

      // 上传成功，补充 url 字段
      if (progress.status === "success" && progress.data?.url) {
        currentFile.url = progress.data.url;

        // 更新 ruleForm.cover，确保无 Proxy
        if (progress.data.fileIdentifier) {
          fileListAPi.value = fileListAPi.value.filter(
            item => item.uid !== file.uid
          );
          fileListAPi.value.push({
            fileIdentifier: progress.data.fileIdentifier,
            fileType: "PHOTO",
            uid: file.uid
          });
        }
      }

      // 失败时移除，成功/上传中则更新
      fileList.value = fileList.value.filter(f => f.uid !== file.uid);
      if (progress.status === "fail") {
        ElMessage.error(progress.errMessage || "上传失败，请重试");
      } else {
        // 用浅拷贝，避免 Proxy
        fileList.value = [...fileList.value, { ...currentFile }];
      }
    });
    // if (code === 200) {
    //   fileListAPi.value.push({
    //     fileIdentifier: data.fileIdentifier,
    //     fileType: "PHOTO",
    //     uid: file.uid,
    //     url: data.url,
    //     fileName: data.fileName
    //   });
    //   ElLoading.service().close();
    // }
  } catch (error) {
    console.error("上传出错:", error);
    return false;
  }
  // const fileType = file.type?.split("/")[0];
  // if (fileType == "image") {
  //   let { data, code } = await uploadFile(file);
  //   if (code === 200) {
  //     fileListAPi.value.push({
  //       fileIdentifier: data.fileIdentifier,
  //       fileType: "PHOTO",
  //       uid: file.uid
  //     });
  //   }
  // } else {
  //   ElMessage.error("请上传图片");
  //   return false;
  // }
};
//删除图片视频
const handleRemove = (uploadFile, uploadFiles) => {
  fileListAPi.value = fileListAPi.value.filter(it => it.uid !== uploadFile.uid);
};
const dialogImageUrl = ref("");
const dialogVisible = ref(false);
const handlePictureCardPreview = uploadFile => {
  dialogImageUrl.value = uploadFile.url;
  dialogVisible.value = true;
};
// 取消
const cancel = () => {
  router.replace({
    path: "/course/courseDetails/currentDetails",
    query: {
      infoShow: "作业设计",
      periodId: route.query.periodId
    }
  });
};
const submitLoading = ref(false);
// 保存
const submitForm = async val => {
  if (submitLoading.value) return;
  submitLoading.value = true;
  await saveEditeApi(val);
  submitLoading.value = false;
};
const submitBackLoading = ref(false);
// 保存并返回
const submitFormBack = async val => {
  if (submitBackLoading.value) return;
  submitBackLoading.value = true;
  await saveEditeApi(val);
  submitBackLoading.value = false;
};
// 保存api编辑页
const saveEditeApi = async val => {
  if (!evaluateValue.value && !fileListAPi.value?.length) return;
  const params = {
    coursePeriodId: Number(route.query.periodId)
  };
  if (evaluateValue.value) {
    params.content = evaluateValue.value;
  }
  if (fileListAPi.value?.length > 0) {
    params.files = fileListAPi.value.map((it, index) => {
      return {
        sortOrder: index + 1,
        fileIdentifier: it.fileIdentifier,
        fileType: it.fileType
      };
    });
  }
  const operateLog = {
    operateLogType: "COURSE_MANAGEMENT",
    operateType: `编辑了“${props.periodName}”课期中的作业设计`
  };
  let [err, res] = await requestTo(assignmentDesign(params, operateLog));
  if (res) {
    ElMessage.success("保存成功");
    if (val) {
      if (route.query.fromPage === "courseDetail") {
        router.replace({
          path: "/course/courseDetails",
          query: { id: route.query.courseId }
        });
      } else if (route.query.fromPage === "currentDetail") {
        router.replace({
          path: "/course/courseDetails/currentDetails",
          query: {
            periodId: route.query.periodId,
            courseId: route.query.courseId
          }
        });
      }
    }
  } else {
    ElMessage.success("保存失败");
    console.log("🐬-----err-----", err);
  }
};
const btnData = ref([
  { id: 1, name: "放弃新建" },
  { id: 2, name: "保存到草稿箱" }
]);
const deleteDraft = async () => {
  let operateLogDraft = {
    operateLogType: "COURSE_MANAGEMENT",
    operateType: route.query.draftId
      ? `删除了草稿id为“${route.query.draftId}”的草稿数据`
      : `删除了草稿id为“${useCourseStore.draftId}”的草稿数据`
  };
  const [err, res] = await to(
    draftDelete(
      { id: Number(route.query.draftId) || Number(useCourseStore.draftId) },
      operateLogDraft
    )
  );
  if (res.code === 200) {
    ElMessage.success("删除成功");
  } else {
    ElMessage.error("删除失败");
  }
};
// 退出
const backEvt = (val, it) => {
  if (val === "exit") {
    let titlt =
      it.id === 1
        ? `请确认是否清除当前编辑和提交的所有资料，并删除当前编辑资料在草稿箱中对应的草稿条目`
        : `请确认是否将当前编辑和提交的所有资料保存到草稿箱，并退出编辑页面`;
    ElMessageBox.confirm(`${titlt}`, `退出并${it.name}`, {
      confirmButtonText: "确定",
      cancelButtonText: "取消"
    }).then(() => {
      if (it.id === 1) {
        deleteDraft();
        if (route.query.type === "draft") {
          router.replace("/course/drafts");
        } else if (route.query.type === "edite") {
          router.replace("/course/courseDetails");
        } else {
          router.go(-1);
        }
      } else if (it.id === 2) {
        saveApi(false);
        if (route.query.type === "draft") {
          router.replace("/course/drafts");
        } else if (route.query.type === "edite") {
          router.replace("/course/courseDetails");
        } else {
          router.go(-1);
        }
      }
    });
  } else {
    if (route.query.fromPage === "courseDetail") {
      router.replace({
        path: "/course/courseDetails",
        query: { id: route.query.courseId }
      });
    } else if (route.query.fromPage === "currentDetail") {
      router.replace({
        path: "/course/courseDetails/currentDetails",
        query: {
          periodId: route.query.periodId,
          courseId: route.query.courseId
        }
      });
    }
  }
};
const draftLoading = ref(false);
// 保存api
const saveApi = async (val = true) => {
  if (!evaluateValue.value && !fileListAPi.value?.length) return;
  const params = {
    draftId: Number(route.query.draftId) || Number(useCourseStore.draftId)
  };
  if (evaluateValue.value) {
    params.content = evaluateValue.value;
  }
  if (fileListAPi.value?.length > 0) {
    params.files = fileListAPi.value.map((it, index) => {
      return {
        sortOrder: index + 1,
        fileIdentifier: it.fileIdentifier,
        fileType: it.fileType
      };
    });
  }
  const operateLog = {
    operateLogType: "COURSE_MANAGEMENT",
    operateType: `将“${props.periodName}”课期中的作业设计保存在草稿箱`
  };
  // if(!evaluateValue.value && !fileListAPi.value?.length > 0){
  //   ElMessage({
  //     type: "error",
  //     message: "请输入内容"
  //   });
  //   return
  // }
  let [err, res] = await to(saveDraftAssignmentDesign(params, operateLog));
  if (res.code === 200) {
    // console.log("🐠res--------保存草稿箱---------------------->", res);
    ElMessage.success("当前资料已保存到草稿箱");
    if (val) {
      router.go(-1);
    }
  } else {
    ElMessage.error("保存失败", res.msg);
  }
  if (err) {
    console.log("🐬-----err-----", res.msg);
  }
};
// 保存草稿箱
const submitDraftForm = async () => {
  if (draftLoading.value) return;
  draftLoading.value = true;
  await saveApi(false);
  draftLoading.value = false;
};
// 查询作业设计草稿
const assignmentDesignFindByDraftId = async () => {
  const params = {
    draftId: Number(route.query.draftId) || Number(useCourseStore.draftId)
  };

  let [err, res] = await requestTo(draftAssignmentDesignFindByDraftId(params));
  if (res) {
    // console.log("🦄res----------作业设计草稿-------------------->", res);
    evaluateValue.value = res?.content;
    oldEvaluateValue.value = res?.content || "";
    if (res.files?.length) {
      res.files.map(item => {
        fileListAPi.value.push(item?.uploadFile);
        fileList.value.push(item?.uploadFile);
        oldFileList.value.push(item?.uploadFile);
      });
    }
    console.log("🐳-----res-----", res);
  } else {
    console.log("🐬-----err-----", err);
  }
};
// 下一步作业设计草稿
const nextSubmitLoading = ref(false);
const nextSubmitForm = async () => {
  if (nextSubmitLoading.value) return;
  nextSubmitLoading.value = true;
  await saveApi(false);
  nextSubmitLoading.value = false;
  emites("baseInfo", {
    infoShow: "材料说明",
    draftId: Number(route.query.draftId) || Number(useCourseStore.draftId),
    complete: true
  });
};
const lastSubmitLoading = ref(false);
// 上一步作业设计草稿
const lastSubmitForm = async () => {
  if (lastSubmitLoading.value) return;
  lastSubmitLoading.value = true;
  await saveApi(false);
  lastSubmitLoading.value = false;
  emites("baseInfo", {
    infoShow: "课期知识点",
    draftId: Number(route.query.draftId) || Number(useCourseStore.draftId)
  });
};
onMounted(() => {
  if (route.query.type === "edite") {
    getDetail();
  } else {
    assignmentDesignFindByDraftId();
  }
});

// 用于草稿箱点击作业设计校验
defineExpose({
  submitDraftForm
});
</script>

<template>
  <!-- <DescriptionList
      :periodId="Number(route.query.periodId)"
      @name="periodNameEvt"
    /> -->
  <div class="schedulings">
    <div class="content">
      <el-input
        v-model.trim="evaluateValue"
        :rows="9"
        type="textarea"
        resize="none"
        maxlength="500"
        show-word-limit
        :placeholder="evaluateValue ? evaluateValue : '请输入作业'"
      />
    </div>
    <div class="work-img">
      <div class="title">作业配图</div>
      <div class="banner">
        <el-upload
          v-model:file-list="fileList"
          action="#"
          accept="image/*"
          :http-request="() => {}"
          list-type="picture-card"
          :before-upload="beforeUpload"
          :on-remove="handleRemove"
          :on-preview="handlePictureCardPreview"
          :class="{ hideUploadBtn: fileList.length >= 9 }"
        >
          <el-icon><Plus /></el-icon>
        </el-upload>
        <el-dialog v-model="dialogVisible">
          <img class="w-full" :src="dialogImageUrl" alt="Preview Image">
        </el-dialog>
        <div class="img-text">{{ imgText }}</div>
      </div>
      <div class="buttons">
        <div v-if="route.query.type === 'edite'" class="left">
          <el-button class="cancel" @click="backEvt('back')"> 返回 </el-button>
          <el-button
            type="primary"
            class="create"
            :loading="submitBackLoading"
            @click="submitFormBack(true)"
          >
            {{ "保存并返回" }}
          </el-button>
          <el-button
            type="primary"
            class="create"
            :loading="submitLoading"
            @click="submitForm(false)"
          >
            {{ "保存" }}
          </el-button>
        </div>
        <div v-else class="left">
          <!-- <el-button class="cancel" @click="backEvt('exit')"> 退出 </el-button> -->
          <el-dropdown>
            <el-button style="margin-right: 10px">退出</el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item
                  v-for="it in btnData"
                  :key="it.id"
                  @click="backEvt('exit', it)"
                >
                  {{ it.name }}
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
          <el-button
            type="primary"
            class="create"
            :loading="draftLoading"
            @click="submitDraftForm()"
          >
            {{ "保存草稿箱" }}
          </el-button>
          <el-button
            type="primary"
            class="create"
            :loading="lastSubmitLoading"
            @click="lastSubmitForm()"
          >
            {{ "上一步" }}
          </el-button>
          <el-button
            type="primary"
            class="create"
            :loading="nextSubmitLoading"
            @click="nextSubmitForm()"
          >
            {{ "下一步" }}
          </el-button>
        </div>
        <div class="right">
          <el-button
            type="primary"
            class="create"
            @click="aiNewPage(infoShowEnName)"
          >
            {{ "AI课程设计" }}
          </el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.schedulings {
  width: 100%;
  height: 100%;
  // height: 650px;
  background: #fff;
  padding: 20px 0 0 0;
  box-sizing: border-box;
  position: relative;
  .content {
    margin-bottom: 30px;
  }

  .work-img {
    .title {
      margin-bottom: 16px;
      font-size: 14px;
      color: rgb(16 16 16 / 100%);
    }
  }
  .buttons {
    display: flex;
    width: 100%;
    position: absolute;
    bottom: 0;
    left: 0;
    justify-content: space-between;
  }
  .img-text {
    font-size: 12px;
    color: #8c939d;
  }
}
.upload_text {
  font-size: 12px;
  position: relative;
  top: 5px;
  color: #8c939d;
}
:deep(.el-button:focus-visible) {
  display: none;
}
:deep(.hideUploadBtn .el-upload--picture-card) {
  display: none;
}
</style>
