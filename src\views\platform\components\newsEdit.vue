<script setup>
import { ref, reactive, onMounted } from "vue";
import { useRouter, useRoute } from "vue-router";
import RichEditor from "@/components/Base/RichEditor.vue";
import { uploadFile, isOverSizeLimit } from "@/utils/upload/upload.js";
import { Plus } from "@element-plus/icons-vue";
import { ElMessage, ElMessageBox, genFileId, ElUpload } from "element-plus";
import dayjs from "dayjs";
// import {
//   discoverFindAll,
//   discoverDelete,
//   discoverAdd,
//   discoverFindById,
//   discoverUpdate
// } from "@/api/findapi.js";
import { newsFindById, newsUpdate } from "@/api/newsapi.js";
const router = useRouter();
const route = useRoute();

const buttonLoading = ref(false); // 按钮加载状态
const ruleFormRef = ref();
const form = ref({
  id: "",
  fileVOS: [],
  fileList: [],
  title: "标题",
  content: "内容",
  createdAt: ""
});
const customerType = {
  PLATFORM_ADMIN: "平台管理员",
  ORGANIZATION_ADMIN: "组织管理员",
  PARENT: "家长",
  STUDENT: "学生",
  EDUCATION_BUREAU: "教育局"
};

const formData = ref([
  {
    label: "创建时间",
    type: "text",
    prop: "createdAt",
    show: true,
    check: false,
    // placeholder: "请输入姓名",
    width: "400px"
  },
  // 根据新接口，移除发布者姓名和用户类型字段
  // {
  //   label: "发布者姓名",
  //   type: "text",
  //   prop: "promoterName",
  //   show: true,
  //   check: false,
  //   // placeholder: "请输入姓名",
  //   width: "400px"
  // },
  // {
  //   label: "用户类型",
  //   type: "text",
  //   prop: "userType",
  //   show: true,
  //   check: false,
  //   // placeholder: "请输入姓名",
  //   width: "400px"
  // },
  {
    label: "标题",
    type: "input",
    prop: "title",
    show: true,
    check: true,
    placeholder: "请输入标题",
    width: "450px"
  },
  // 根据新接口，暂时注释掉图片上传功能
  // {
  //   label: "图片",
  //   type: "elUpload",
  //   prop: "fileList",
  //   show: true,
  //   check: false,
  //   placeholder: "请上传图片",
  //   width: "400px"
  // },
  {
    label: "内容",
    type: "RichEditor",
    prop: "content",
    show: true,
    check: true,
    isView: true,
    placeholder: "请输入内容",
    width: "400px"
  }
]);

// 自定义富文本校验方法
const validateIntroduction = (rule, value, callback) => {
  console.log("🍭-----rule, value, callback-----", value);
  // 移除HTML标签并检查内容是否为空
  const cleanValue = value.replace(/<[^>]*>/g, "").trim();
  if (cleanValue === "") {
    callback(new Error("内容不能为空"));
  } else {
    callback();
  }
};
const rules = reactive({
  // fileList: [{ required: true, message: "请上传图片", trigger: "blur" }], // 根据新接口，图片不是必填项
  title: [{ required: true, message: "请输入标题", trigger: "blur" }],
  content: [
    { required: true, validator: validateIntroduction, trigger: "blur" }
  ]
});

const removeEmptyValues = obj => {
  return Object.fromEntries(
    Object.entries(obj).filter(
      ([key, value]) =>
        value !== "" && !(Array.isArray(value) && value.length === 0)
    )
  );
};

function setFilesFn(val) {
  let arr = [];
  for (let index = 0; index < val.length; index++) {
    const element = val[index].fileIdentifier;
    arr.push({
      fileType: "COVER",
      fileIdentifier: element,
      sortOrder: index + 1
    });
  }
  return arr;
}

// 图片上传
const fileUpload = async (file, row) => {
  if (form.value.fileList.length > 8) {
    return ElMessage({ type: "warning", message: "上传图片为最多9张" });
  }
  // let isSize = isOverSizeLimit(file, 10);
  // if (isSize.valid) {
  try {
    // 检查文件大小，10MB = 10 * 1024 * 1024 字节
    const maxSize = 10 * 1024 * 1024; // 10MB
    if (file.size > maxSize) {
      ElMessage.error("上传图片大小不能超过10MB！");
      return false; // 阻止上传
    }
    const { code, data } = await uploadFile(file, () => {}, ["image"]);
    if (code === 200) {
      form.value.fileList.push({
        name: data.fileName,
        url: data.url,
        fileIdentifier: data.fileIdentifier
      });
      form.value.fileVOS.push({
        fileType: "COVER",
        fileIdentifier: data.fileIdentifier,
        sortOrder: form.value.fileList.length
      });
    }
  } catch (error) {
    console.error("上传失败:", error);
    ElMessage.error("上传失败，请重试");
  }
  // } else {
  //   ElMessage.error(isSize.message);
  // }
};

// 删除图片
const handleRemove = (file, fileList) => {
  console.log("🍭-----file, fileList-----", file, fileList);
  form.value.fileList = fileList;
  form.value.fileVOS = setFilesFn(fileList);
};

// 获取新闻详情
const onDetails = async val => {
  try {
    const { code, data } = await newsFindById({ id: val.id });
    if (code === 200) {
      // 根据实际响应，成功时code为200
      console.log("🎁-----data-----", data);
      form.value.id = data.id;
      form.value.createdAt = dayjs(data.createdAt).format(
        "YYYY-MM-DD HH:mm:ss"
      );
      form.value.title = data.title;
      form.value.content = data.content;
      // 新接口没有promoterName和userType字段，移除相关处理
      // form.value.promoterName = data.promoterName;
      // form.value.userType = data.userType;

      // 新接口暂时没有文件相关字段，注释掉文件处理逻辑
      // if (data.files?.length) {
      //   data.files.map(item => {
      //     form.value.fileList.push({
      //       url: item.uploadFile.url,
      //       name: item.uploadFile.fileName,
      //       fileIdentifier: item.uploadFile.fileIdentifier
      //     });
      //     form.value.fileVOS.push({
      //       fileType: "COVER",
      //       fileIdentifier: item.uploadFile.fileIdentifier,
      //       sortOrder: form.value.fileList.length
      //     });
      //   });
      // }
    } else {
      ElMessage({
        type: "error",
        message: "获取新闻详情失败"
      });
    }
  } catch (error) {
    console.error("获取新闻详情失败:", error);
    ElMessage({
      type: "error",
      message: "获取新闻详情失败，请重试"
    });
  }
};

// 编辑保存
const editAdd = () => {
  ruleFormRef.value.validate(async valid => {
    if (valid) {
      buttonLoading.value = true;
      try {
        // 根据新接口规范，传递id、title和content
        const paramsArg = {
          id: form.value.id,
          title: form.value.title,
          content: form.value.content
        };

        // 移除空值
        let aee = removeEmptyValues(paramsArg);

        // 操作日志
        const operateLog = {
          operateLogType: "PLATFORM_SETTINGS",
          operateType: "编辑了标题为" + form.value.title + "的新闻管理"
        };

        // 调用新闻编辑API
        const { code } = await newsUpdate(aee, operateLog);

        if (code === 200) {
          // 根据实际响应，成功时code为200
          ElMessage({
            type: "success",
            message: "编辑成功"
          });
          router.go(-1);
        } else {
          ElMessage({
            type: "error",
            message: "编辑失败"
          });
        }
      } catch (error) {
        console.error("编辑新闻失败:", error);
        ElMessage({
          type: "error",
          message: "编辑失败，请重试"
        });
      } finally {
        buttonLoading.value = false;
      }
    } else {
      console.log("error submit!!");
      return false;
    }
  });
};

onMounted(() => {
  const id = { id: route.query.id };
  onDetails(id);
});
</script>

<template>
  <div class="commonapp">
    <!-- <div class="formbox">新闻编辑</div> -->
    <el-scrollbar class="scrollbar">
      <div class="table_content">
        <el-form ref="ruleFormRef" :rules="rules" :model="form">
          <el-descriptions title="" :column="1" border :label-width="'15%'">
            <el-descriptions-item
              v-for="(item, index) in formData"
              :key="index"
              label-align="center"
              label-class-name="my-label"
            >
              <template #label>
                <span v-if="item.check" class="star">*</span> {{ item.label }}
              </template>
              <el-form-item
                :prop="item.prop"
                :inline-message="item.check"
                style="margin-bottom: 0"
                :show-message="true"
                error-placement="right"
              >
                <!-- input输入 -->
                <template v-if="item.type === 'input'">
                  <el-input
                    v-model.trim="form[item.prop]"
                    :placeholder="item.placeholder"
                    :style="{ width: item.width }"
                  />
                </template>
                <!-- input输入 -->
                <template v-else-if="item.type === 'text'">
                  <span v-if="item.prop === 'userType'">
                    {{ customerType[form[item.prop]] }}
                  </span>
                  <span v-else>{{ form[item.prop] }}</span>
                </template>
                <!-- 图片上传 -->
                <template v-else-if="item.type === 'elUpload'">
                  <el-upload
                    v-model:file-list="form.fileList"
                    action="#"
                    list-type="picture-card"
                    :auto-upload="false"
                    :on-change="fileUpload"
                    :on-remove="handleRemove"
                    :limit="9"
                  >
                    <el-icon><Plus /></el-icon>
                  </el-upload>
                </template>
                <!-- 富文本 -->
                <template v-else-if="item.type === 'RichEditor'">
                  <RichEditor
                    v-model="form[item.prop]"
                    height="325px"
                    :isOpen="item.isView"
                    :isShield="true"
                    :excludeKeys="['headerSelect', 'fontSize', 'lineHeight']"
                    :lineHeight="2"
                  />
                </template>
              </el-form-item>
            </el-descriptions-item>
          </el-descriptions>
        </el-form>
      </div>
    </el-scrollbar>

    <div class="footer">
      <el-button @click="router.go(-1)">取消</el-button>
      <el-button :loading="buttonLoading" type="primary" @click="editAdd">
        保存
      </el-button>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.commonapp {
  // margin: 20px;
  //   display: flex;
  //   align-items: flex-start;
  //   height: 72vh;
  //   max-width: 990px;
  background-color: #fff;
  padding: 20px;

  .scrollbar {
    // padding-top: 20px;
    margin-bottom: 20px;
    height: calc(100vh - 206px);
    background-color: #fff;
  }

  .formbox {
    margin-bottom: 20px;
  }
  .footer {
    display: flex;
    justify-content: end;
  }
  // .table_content {
  // margin-bottom: 20px;
  // }
}

.star {
  margin-right: 3px;
  color: red;
}

:deep(.el-descriptions__label.el-descriptions__cell.is-bordered-label) {
  background: #e1f5ff;
}
</style>
