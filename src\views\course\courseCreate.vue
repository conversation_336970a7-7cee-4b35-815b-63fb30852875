<script setup>
import { ref, onMounted, watch, nextTick } from "vue";
import CoursePeriodTrip from "./components/coursePeriodTrip.vue";
import BaseInfo from "./components/baseInfo.vue";
import PriceSetting from "./components/priceEdite.vue";
import courseIntroduction from "./components/introductionEdite.vue";
import JobDesign from "./components/homeWorkEdited.vue";
import Complete from "./components/complete.vue";
import InstructionText from "./components/instructionText.vue";
import KnowledgePoints from "./components/knowledgePoints.vue";

import { useRouter, useRoute } from "vue-router";
import { ElMessage } from "element-plus";
import { createCourse } from "@/utils/createTestData.js";
import { to, debounce } from "@iceywu/utils";
import { draftFindFlowsByDraftId } from "@/api/drafts.js";
import { courseStore } from "@/store/modules/course.js";
import { whitePath } from "@/utils/common.js";
const useCourseStore = courseStore();

createCourse();

const router = useRouter();
const route = useRoute();

// 批量导入所有图片
const images = import.meta.glob(
  "/src/assets/draftsIcon/*.{png,jpg,jpeg,gif,svg}",
  { eager: true }
);
// 或者转换为更友好的格式
const imageModules = Object.fromEntries(
  Object.entries(images).map(([path, module]) => [
    path.split("/").pop().split(".")[0], // 提取文件名作为key
    module.default
  ])
);
// 方法1：处理成按前缀分组的对象
function groupByPrefix(data) {
  const grouped = {};
  Object.entries(data).forEach(([key, value]) => {
    const [prefix, suffix] = key.split("_");
    if (!grouped[prefix]) {
      grouped[prefix] = {};
    }
    grouped[prefix][suffix] = value;
  });
  return grouped;
}
// 所有tab切换的icon
const imageIcon = groupByPrefix(imageModules);

// tab切换
const tabInfo = ref([
  {
    id: 1,
    label: "基础信息",
    name: "foundation",
    must: false,
    type: "A",
    icon: groupByPrefix(imageModules).foundation.A
  },
  {
    id: 2,
    label: "课期行程",
    name: "trip",
    must: false,
    type: "C",
    icon: groupByPrefix(imageModules).trip.C
  },
  {
    id: 3,
    label: "课期介绍",
    name: "introduce",
    must: false,
    type: "C",
    icon: groupByPrefix(imageModules).introduce.C
  },
  {
    id: 4,
    label: "课期知识点",
    name: "knowledge",
    must: false,
    type: "C",
    icon: groupByPrefix(imageModules).knowledge.C
  },
  {
    id: 5,
    label: "作业设计",
    name: "task",
    must: false,
    type: "C",
    icon: groupByPrefix(imageModules).task.C
  },
  {
    id: 6,
    label: "材料说明",
    name: "equipment",
    must: false,
    type: "C",
    icon: groupByPrefix(imageModules).equipment.C
  },
  {
    id: 7,
    label: "注意事项",
    name: "matter",
    must: false,
    type: "C",
    icon: groupByPrefix(imageModules).matter.C
  },
  {
    id: 8,
    label: "用户协议",
    name: "agreement",
    must: false,
    type: "C",
    icon: groupByPrefix(imageModules).agreement.C
  },
  {
    id: 9,
    label: "价格设置",
    name: "price",
    must: false,
    type: "C",
    icon: groupByPrefix(imageModules).price.C
  },
  {
    id: 10,
    label: "完成",
    name: "complete",
    must: false,
    type: "C",
    icon: groupByPrefix(imageModules).complete.C
  }
]);

const infoShow = ref("基础信息");
const infoShowEnName = ref("foundation");
const activeIndex = ref(0);
const text = ref("这里是该页面的信息填写说明以及填写建议");
const dialogImageUrl = ref("");
const dialogVisible = ref(false);
const submitLoading = ref(false);
const baseInfoSend = ref({});
const baseInfo = val => {
  // console.log("🍪-----val-----", val);
  let obj = tabInfo.value.find(it => it.label === val.infoShow);
  let ind = Number(obj.id) - 1;
  if (val.complete) {
    baseInfoSend.value = val;
    next(obj, ind, val, true);
  } else {
    next(obj, ind, val, true);
    baseInfoSend.value = val;
  }
};
const baseInfoPeriod = val => {
  baseInfoSend.value.periodName = val;
};
function setUpIcon(params) {
  if (params.type == "A") {
    return (params.icon = imageIcon[params.name]?.A);
  } else if (params.type == "B") {
    return (params.icon = imageIcon[params.name]?.B);
  } else if (params.type == "C") {
    return (params.icon = imageIcon[params.name]?.C);
  }
}
const draftFlowType = {
  BASIC_INFORMATION: "基础信息",
  COURSE_ITINERARY: "课期行程",
  COURSE_INTRODUCTION: "课期介绍",
  COURSE_KNOWLEDGE_POINT: "课期知识点",
  ASSIGNMENT_DESIGN: "作业设计",
  EQUIPMENT_DESCRIPTION: "材料说明",
  PRECAUTIONS: "注意事项",
  USER_AGREEMENT: "用户协议",
  PRICE_SETTING: "价格设置"
};

// 根据草稿Id查询草稿完成度
const draftApiV1 = async (val, t, i) => {
  let [err, res] = await to(draftFindFlowsByDraftId({ draftId: val }));
  if (res) {
    tabInfo.value.map(it => {
      it.type = "C";
      it.icon = setUpIcon(it);
    });
    const { data } = res;
    tabInfo.value.map(it => {
      data?.map(item => {
        if (it.label === draftFlowType[item.draftFlowType]) {
          if (item.completed) {
            it.type = "B";
            it.icon = setUpIcon(it);
            it.must = item.completed;
          } else {
            it.type = "C";
            it.icon = setUpIcon(it);
            it.must = item.completed;
          }
        } else {
        }
      });
    });
    handleStepNavigation(t, i);
  } else {
  }
};
const foundationTest = ref(); // ref 基础信息
const tripTest = ref(); // ref 课期行程
const introduceTest = ref(); // ref 课期介绍
const agreementTest = ref(); // ref 用户协议
const priceTest = ref(); // ref 价格设置
const task = ref(); // ref 作业设计
const knowledge = ref(); // ref 课期知识点
const equipment = ref(); // ref 材料说明
const matter = ref(); // ref 注意事项

/**
 * 处理步骤导航的函数
 * @param {Object} t - 目标步骤对象
 * @param {number} i - 步骤索引
 */
function handleStepNavigation(t, i) {
  if (!t) return;
  // console.log("🐠-----目标步骤-----", t, i);

  // 定义步骤依赖关系配置
  const STEP_DEPENDENCIES = {
    基础信息: [],
    课期行程: ["基础信息"],
    课期介绍: ["基础信息", "课期行程"],
    课期知识点: ["基础信息", "课期行程", "课期介绍"],
    作业设计: ["基础信息", "课期行程", "课期介绍"],
    材料说明: ["基础信息", "课期行程", "课期介绍"],
    注意事项: ["基础信息", "课期行程", "课期介绍"],
    用户协议: ["基础信息", "课期行程", "课期介绍"],
    价格设置: ["基础信息", "课期行程", "课期介绍", "用户协议"],
    完成: ["基础信息", "课期行程", "课期介绍", "用户协议", "价格设置"]
  };

  /** 获取步骤在tabInfo中的索引 */
  const getStepIndex = stepLabel => {
    return tabInfo.value.findIndex(item => item.label === stepLabel);
  };

  /** 检查指定步骤的所有依赖是否已完成 */
  const getUncompletedDependencies = stepLabel => {
    const dependencies = STEP_DEPENDENCIES[stepLabel] || [];
    return dependencies.filter(depLabel => {
      const stepIndex = getStepIndex(depLabel);
      return stepIndex === -1 || !tabInfo.value[stepIndex]?.must;
    });
  };

  /** 激活指定步骤 */
  const activateStep = (stepLabel, stepIndex) => {
    active.value = stepIndex;
    infoShow.value = stepLabel;
    iconNameV1(t, stepIndex);
  };

  /** 跳转到指定步骤并显示错误提示 */
  const redirectToStep = async targetLabel => {
    const targetIndex = getStepIndex(targetLabel);
    if (targetIndex !== -1) {
      active.value = targetIndex;
      infoShow.value = targetLabel;
      iconNameV1(tabInfo.value[targetIndex], targetIndex);
      ElMessage.error({
        type: "error",
        message: `请先完成：${targetLabel}`
      });
      if (targetLabel === "基础信息") {
        setTimeout(() => {
          foundationTest.value?.submitForm(true);
        }, 500);
      }
    }
  };
  const targetStepLabel = t.label;
  const dependencies = STEP_DEPENDENCIES[targetStepLabel];
  // 如果目标步骤没有依赖，直接激活
  if (!dependencies || dependencies.length === 0) {
    activateStep(targetStepLabel, i);
    return;
  }
  // 检查依赖完成情况
  const uncompletedDeps = getUncompletedDependencies(targetStepLabel);
  if (uncompletedDeps.length === 0) {
    // 所有依赖已完成，激活目标步骤
    activateStep(targetStepLabel, i);
  } else {
    // 存在未完成的依赖，跳转到第一个未完成的依赖步骤
    redirectToStep(uncompletedDeps[0]);
  }
}

router.afterEach((to, from) => {
  if (whitePath.includes(to.path)) {
    useCourseStore.saveDraftId("");
  }
});
const ddd = ref([]);
onMounted(() => {
  nextTick();
  // console.log("🌈-----route.query-----", route.query);
  if (route.query?.type === "draft") {
    baseInfoSend.value.draftId = route.query.draftId;
    const obj = {
      id: 1,
      label: "基础信息",
      name: "foundation",
      must: false,
      type: "A",
      icon: groupByPrefix(imageModules).foundation.A
    };
    setTimeout(() => {
      draftApiV1(route.query.draftId, obj, 0);
    }, 200);
  } else if (route.query?.type === "create") {
    baseInfoSend.value.draftId = useCourseStore.draftId || "";
    if (baseInfoSend.value.draftId !== "") {
      const obj = {
        id: 1,
        label: "基础信息",
        name: "foundation",
        must: false,
        type: "A",
        icon: groupByPrefix(imageModules).foundation.A
      };
      setTimeout(() => {
        draftApiV1(baseInfoSend.value.draftId, obj, 0);
      }, 200);
    }
  } else if (route.query?.type === "createPeriod") {
    baseInfoSend.value.draftId = useCourseStore.draftId || "";
    if (baseInfoSend.value.draftId !== "") {
      const obj = {
        id: 1,
        label: "基础信息",
        name: "foundation",
        must: false,
        type: "A",
        icon: groupByPrefix(imageModules).foundation.A
      };
      setTimeout(() => {
        draftApiV1(baseInfoSend.value.draftId, obj, 0);
      }, 200);
    }
  }
});

// 课期行程的上/下一页
const upperBelowEvt = value => {
  baseInfoSend.value.draftId = value.draftId;
  if (value.type === "upper") {
    let obj = tabInfo.value.find(it => it.label === value.infoShow);
    let ind = Number(obj.id) - 1;
    next(obj, ind, value, true);
  } else {
    if (value.complete) {
      let obj = tabInfo.value.find(it => it.label === value.infoShow);
      let ind = Number(obj.id) - 1;
      next(obj, ind, value, true);
    }
  }
};
// 完成的下一页
const completeBelowEvt = value => {
  baseInfoSend.value.draftId = value.draftId;
  let obj = tabInfo.value.find(it => it.label === value.infoShow);
  let ind = Number(obj.id) - 1;
  next(obj, ind, value, true);
};
watch(() => {
  if (baseInfoSend.value && baseInfoSend.value.infoShow) {
    infoShow.value = baseInfoSend.value.infoShow;
  }
});

function iconNameV1(val, index) {
  tabInfo.value.map(it => {
    if (it.label === val.label) {
      it.type = "A";
      it.icon = setUpIcon(it);
    }
  });
  if (val.name === "introduce") {
    text.value =
      "课期介绍采用图文方式展示，建议在课期介绍中展示以下内容：课程的内容、课程的特点、课程的往期开展介绍和照片、当前课期的领队讲师介绍、当前课期的特殊之处。";
  } else {
    text.value = "这里是该页面的信息填写说明以及填写建议";
  }
}

const buu = ref(false);
const saveCheck = async val => {
  buu.value = false;
  switch (val) {
    case "基础信息":
      buu.value = await foundationTest.value?.submitFormatt();
      break;
    case "课期行程":
      buu.value = await tripTest.value?.nextStepApiatt();
      break;
    case "课期介绍":
      buu.value = await introduceTest.value?.submitDraftFormatt();
      break;
    case "课期知识点":
      knowledge.value?.submitDraftFormatt();
      break;
    case "作业设计":
      task.value?.submitDraftForm();
      break;
    case "材料说明":
      equipment.value?.submitDraftFormatt();
      break;
    case "注意事项":
      matter.value?.submitDraftFormatt();
      break;
    case "用户协议":
      buu.value = await agreementTest.value?.submitDraftFormatt();
      break;
    case "价格设置":
      buu.value = await priceTest.value?.submitDraftFormatt();
      break;
    default:
      break;
  }
  return buu.value;
};

const basedraftId = val => {
  baseInfoSend.value = val;
};

// 选中step
const active = ref(0);
const next = debounce(async (item, index, val, bool) => {
  // console.log("🍪-----item, index, val-----", item, index, val);
  // console.log("🐳-----infoShow.value-----", infoShow.value);
  if (!bool) {
    buu.value = await saveCheck(infoShow.value);
  } else {
    buu.value = true;
  }
  if (
    infoShow.value === "课期知识点" ||
    infoShow.value === "作业设计" ||
    infoShow.value === "材料说明" ||
    infoShow.value === "注意事项" ||
    infoShow.value === "完成"
  ) {
    buu.value = true;
  }
  if (buu.value !== true) return;

  infoShowEnName.value = item.name;
  if (route.query?.type === "draft") {
    baseInfoSend.value.draftId = route.query.draftId;
    setTimeout(() => {
      draftApiV1(route.query.draftId, item, index);
    }, 200);
  } else if (route.query?.type === "create") {
    // console.log('🌳-----baseInfoSend.value-----', baseInfoSend.value);
    if (baseInfoSend.value.draftId) {
      setTimeout(() => {
        draftApiV1(baseInfoSend.value.draftId, item, index);
      }, 200);
    } else {
      ElMessage.error({ type: "error", message: "请先完成：基础信息" });
    }
  } else if (route.query?.type === "createPeriod") {
    if (baseInfoSend.value.draftId) {
      setTimeout(() => {
        draftApiV1(baseInfoSend.value.draftId, item, index);
      }, 200);
    } else {
      ElMessage.error({ type: "error", message: "请先完成：基础信息" });
    }
  }
}, 300);
</script>

<template>
  <div class="course-add">
    <div class="stepsbox">
      <el-steps direction="vertical" :active="active" finish-status="process">
        <el-step
          v-for="(item, index) in tabInfo"
          :key="index"
          :title="item.label"
          :icon="item.icon"
          @click="next(item, index)"
        />
      </el-steps>
    </div>
    <div class="componbox">
      <InstructionText :text="text" />
      <div class="course-container">
        <BaseInfo
          v-if="infoShow === '基础信息'"
          ref="foundationTest"
          :draftId="baseInfoSend.draftId"
          :infoShowEnName="infoShowEnName"
          :periodName="baseInfoSend.periodName"
          @base-info="baseInfo"
          @base-info-period="baseInfoPeriod"
          @basedraft-id="basedraftId"
        />
        <CoursePeriodTrip
          v-if="infoShow === '课期行程'"
          ref="tripTest"
          :draftId="baseInfoSend.draftId"
          :isNewEdit="'new'"
          :infoShowEnName="infoShowEnName"
          :periodName="baseInfoSend.periodName"
          @upper-below-evt="upperBelowEvt"
        />
        <PriceSetting
          v-else-if="infoShow === '价格设置'"
          ref="priceTest"
          :draftId="baseInfoSend.draftId"
          :infoShowEnName="infoShowEnName"
          :periodName="baseInfoSend.periodName"
          @base-info="baseInfo"
        />
        <courseIntroduction
          v-else-if="infoShow === '课期介绍'"
          ref="introduceTest"
          :infoShow="infoShow"
          :draftId="baseInfoSend.draftId"
          :infoShowEnName="infoShowEnName"
          :periodName="baseInfoSend.periodName"
          @base-info="baseInfo"
        />
        <KnowledgePoints
          v-else-if="infoShow === '课期知识点'"
          ref="knowledge"
          :periodName="baseInfoSend.periodName"
          :infoShow="infoShow"
          :infoShowEnName="infoShowEnName"
          :draftId="baseInfoSend.draftId"
          @base-info="baseInfo"
        />
        <courseIntroduction
          v-else-if="infoShow === '材料说明'"
          ref="equipment"
          :periodName="baseInfoSend.periodName"
          :infoShow="infoShow"
          :infoShowEnName="infoShowEnName"
          :draftId="baseInfoSend.draftId"
          @base-info="baseInfo"
        />
        <courseIntroduction
          v-else-if="infoShow === '注意事项'"
          ref="matter"
          :periodName="baseInfoSend.periodName"
          :infoShow="infoShow"
          :infoShowEnName="infoShowEnName"
          :draftId="baseInfoSend.draftId"
          @base-info="baseInfo"
        />
        <courseIntroduction
          v-else-if="infoShow === '用户协议'"
          ref="agreementTest"
          :infoShow="infoShow"
          :infoShowEnName="infoShowEnName"
          :periodName="baseInfoSend.periodName"
          :draftId="baseInfoSend.draftId"
          @base-info="baseInfo"
        />
        <JobDesign
          v-else-if="infoShow === '作业设计'"
          ref="task"
          :draftId="baseInfoSend.draftId"
          :infoShowEnName="infoShowEnName"
          :periodName="baseInfoSend.periodName"
          @base-info="baseInfo"
        />
        <Complete
          v-else-if="infoShow === '完成'"
          :draftId="baseInfoSend.draftId"
          :infoShowEnName="infoShowEnName"
          :periodName="baseInfoSend.periodName"
          @complete-below-evt="completeBelowEvt"
        />
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.course-add {
  box-sizing: border-box;
  font-size: 14px;
  color: #101010;
  padding: 20px 20px;
  background-color: #fff;
  position: relative;
  display: flex;

  .stepsbox {
    height: calc(100vh - 140px);
    min-width: 160px;
    border-right: solid 2px var(--el-border-color);
    margin-right: 20px;
  }

  .componbox {
    width: 100%;
    height: 100%;
    .course-container {
      box-sizing: border-box;
      width: 100%;
      height: calc(100vh - 240px);
      overflow-y: auto;
      padding: 10px 0 0 0;
    }
  }
}

:deep(.el-step__icon) {
  padding: 20px 0px;
  cursor: pointer;
}
:deep(.el-step.is-vertical .el-step__title) {
  margin: 10px 0px 0px;
  cursor: pointer;
}
</style>
