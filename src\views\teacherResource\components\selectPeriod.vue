<script setup>
import { ref, onMounted, onActivated } from "vue";
import { useRouter, useRoute } from "vue-router";
import {
  courseFindId,
  leaderLecturerFind,
  coursePeriodOffline,
  coursePeriodOnline,
  coursePeriodDelete,
  periodOpenGroupOrder,
  courseDelete,
  deletIds
} from "@/api/course.js";
import { periodcancelReview, coursePeriodAll } from "@/api/period.js";
import { draftGetCount } from "@/api/drafts.js";
import { ElMessage, ElMessageBox } from "element-plus";
import { requestTo } from "@/utils/http/tool";
import { formatTime } from "@/utils/index";
import { courseStore } from "@/store/modules/course.js";
import { to, isEmpty } from "@iceywu/utils";
import OrderDialog from "@/components/Base/orderDialog.vue";
import { Warning } from "@element-plus/icons-vue";
import { AUDIT_ENUM } from "@/utils/enum";
import { ImageThumbnail } from "@/utils/imageProxy.js";
import { teachersInviteClass } from "@/api/teachers/teacherResourcePool.js";
import dayjs from "dayjs";
import { useTeachersStore } from "@/store/modules/teachers.js";
defineOptions({
  name: "CourseDetails"
});
const props = defineProps({
  sendCourse: {
    type: Object,
    default: () => ({})
  }
});
const emites = defineEmits(["selectIsCourse"]);
const teachersStore = useTeachersStore();
onActivated(() => {
  getTablePeriodList();
  // getTableList();
});
const useCourseStore = courseStore();
const router = useRouter();
const route = useRoute();
const copyShow = ref(false);
// 表头
const tableHeader = ref([
  {
    id: "1",
    label: "课程名",
    value: "",
    width: "107px"
  },
  {
    id: "2",
    label: "课程ID",
    value: "",
    width: "107px"
  },
  {
    id: "3",
    label: "创建时间",
    value: "",
    width: "107px"
  },
  {
    id: "4",
    label: "课程类型",
    value: "",
    width: "107px"
  },
  {
    id: "5",
    label: "课程亮点标签",
    value: "",
    width: "107px"
  },
  {
    id: "6",
    label: "课程状态",
    value: "",
    width: "107px"
  }
]);
// 表格
const tableData = ref([]);
// 搜索表单
const form = ref({
  startTime: "",
  endTime: "",
  coursePeriodState: "all",
  leadersId: 0,
  lecturersId: 0,
  buyType: "all",
  reviewState: "all"
});
const url = ref();

const srcList = ref([]);
// 课期状态
const stateOptions = [
  {
    value: "all",
    label: "全部"
  },
  {
    value: "ONLINE",
    label: "上架"
  },
  {
    value: "NOT_LISTED",
    label: "未上架"
  },
  {
    value: "ONLINE_UNDER_REVIEW",
    label: "上架审核"
    // label: "审核中"
  },
  {
    value: "OFFLINE",
    label: "下架"
  },
  {
    value: "OFFLINE_UNDER_REVIEW",
    label: "下架审核"
    // label: "审核中"
  },
  {
    value: "COMPLETED",
    label: "已完成"
  }
];
// 审核状态
const auditOptions = [
  {
    value: "all",
    label: "全部"
  },
  {
    value: "ONLINE_UNDER_REVIEW",
    label: "上架审核中"
  },
  {
    value: "ONLINE_PASS",
    label: "上架通过"
  },
  {
    value: "ONLINE_REJECT",
    label: "上架驳回"
    // label: "审核中"
  },
  {
    value: "OFFLINE_UNDER_REVIEW",
    label: "下架审核中"
  },
  {
    value: "OFFLINE_PASS",
    label: "下架通过"
    // label: "审核中"
  },
  {
    value: "OFFLINE_REJECT",
    label: "下架驳回"
    // label: "审核中"
  },
  {
    value: "NONE",
    label: "无"
  }
];
// 获取课程状态
const getSatte = val => {
  let res = "";
  stateOptions?.map(item => {
    if (item.value === val) {
      res = item.label;
    }
  });
  return res;
};
// 讲师
const teacherOptions = ref([{ label: "全部", value: 0 }]);
// 领队
const leaderOptions = ref([{ label: "全部", value: 0 }]);
// 购买类型
const typeOptions = [
  {
    value: "all",
    label: "全部"
  },
  {
    value: "ORDINARY",
    label: "普通单"
  },
  {
    value: "PRIVATE_DOMAIN_GROUP_ORDER",
    label: "团购单"
  }
];

const pickTime = ref("");

const params = ref({
  page: 1,
  size: 15,
  sort: "createdAt,desc",
  totalElements: 0
});

const isOffline = ref(true);
// 获取课期列表信息
const gettLoading = ref(false);
const getTablePeriodList = async data => {
  if (gettLoading.value) {
    return;
  }
  gettLoading.value = true;
  let paramsData = {
    page: params.value.page - 1,
    size: params.value.size,
    sort: params.value.sort,
    courseId: route.query.courseId
  };
  for (const paramsDataKey in form.value) {
    let isArray = Array.isArray(form.value[paramsDataKey]);
    if (isArray) {
      if (form.value[paramsDataKey].length > 0) {
        paramsData[paramsDataKey] = form.value[paramsDataKey];
      }
    } else {
      if (form.value[paramsDataKey]) {
        paramsData[paramsDataKey] = form.value[paramsDataKey];
      }
    }
  }
  if (form.value.coursePeriodState === "all") {
    delete paramsData.coursePeriodState;
  }
  if (form.value.buyType === "all") {
    delete paramsData.buyType;
  }
  if (form.value.reviewState === "all") {
    delete paramsData.reviewState;
  }
  const [err, result] = await requestTo(coursePeriodAll(paramsData));
  if (result) {
    tableData.value = result?.content;
    // result?.content.forEach(it => {
    //   if (
    //     it.coursePeriodState === "COMPLETED" ||
    //     it.coursePeriodState === "OFFLINE_UNDER_REVIEW" ||
    //     it.coursePeriodState === "ONLINE" ||
    //     it.coursePeriodState === "ONLINE_UNDER_REVIEW"
    //   ) {
    //     isOffline.value = false;
    //   }
    // });
    params.value.totalElements = result.totalElements;
  } else {
    ElMessage.error(err);
  }
  gettLoading.value = false;
};
//分页
const handleCurrentChange = e => {
  params.value.page = e;
  getTablePeriodList();
};
const handleSizeChange = val => {
  params.value.page = 1;
  params.value.size = val;
  getTablePeriodList();
};

// 详情
const detailEvt = row => {
  // console.log("💗getInfoid---------->", row);
  router.push({
    path: "/teachers/current/details",
    query: { courseId: route.query.id, periodId: row.id }
  });
  useCourseStore.savePeriodState(row.coursePeriodState);
};
const operateLog = ref({});

// 获取领队讲师姓名
const getName = val => {
  let res = [];
  if (!val?.length) return;
  val.map(item => {
    res.push(item.name);
  });
  return res.join("、");
};
// 取消
const cancelEvt = () => {
  if (route.query.type === "teachers") {
    emites("selectIsCourse", true);
  }
};
// 确认邀请  单选
const batchInvite = () => {
  // console.log(
  //   "🎉selectOnly.value------------------------------>",
  //   selectOnly.value
  // );
  if (isEmpty(selectOnly.value)) {
    ElMessage.error("请选择要邀请的课期");
    return;
  }
  ElMessageBox.confirm(
    `确定要邀请“${teachersStore.teachersInfo?.name}”参与“${selectOnly.value?.name}”课期吗？`,
    "邀请参课",
    {
      confirmButtonText: "确定",
      cancelButtonText: "取消"
    }
  )
    .then(async () => {
      const operateLog = {
        operateLogType: "TEACHER_DATABASE",
        operateType: `邀请了“${teachersStore.teachersInfo?.name}”参与“${selectOnly.value?.name}"课期`
      };
      let [err, res] = await to(
        teachersInviteClass(
          {
            coursePeriodId: +selectOnly.value.id,
            teacherId: +teachersStore.teachersInfo?.teacherId
          },
          operateLog
        )
      );
      if (res.code === 200) {
        ElMessage.success("邀请成功");
        selectOnly.value = {};
        getTablePeriodList();
        router.replace({ path: "/teacher/resource/pool/index" });
      } else {
        ElMessage.error("邀请失败");
      }
      if (err) {
        ElMessage.error("邀请失败");
      }
    })
    .catch(() => {});
};
// 批量
let batchDeleteArr = ref([]);
const tableRef = ref(null);
const selectable = row => {
  if (
    (row.coursePeriodState === "OFFLINE" && row.isExistInformation === false) ||
    row.coursePeriodState === "NOT_LISTED"
  ) {
    return row.id;
  }
};
const getRowKeys = row => {
  return row.id;
};
const handleSelectionChange = val => {
  batchDeleteArr.value = [];
  ids.value = [];
  course.value = [];
  if (!val.length) return;
  if (val.length > 0) {
    val.forEach(item => {
      batchDeleteArr.value.push(item);
    });
  } else {
    batchDeleteArr.value = val[0];
  }
};
let ids = ref([]);
let course = ref([]);
// 单选相关
const selectedRowId = ref(null);
const selectOnly = ref({});
const handleSelectRow = row => {
  // 这里可以处理选中行的逻辑，例如存储选中的行数据
  // selectedRowId.value = row.id
  selectOnly.value = row;
};
// 确认发布
const batchDelete = () => {
  batchDeleteArr.value.forEach(item => {
    ids.value.push(item.id);
    course.value.push(item.name);
  });
  if (ids.value.length === 0) {
    ElMessage.error("请选择要发布的课期");
    return;
  }
  ElMessageBox.confirm(
    `确定要发布“${course.value.join("、")}”课期吗？`,
    "发布课期",
    {
      confirmButtonText: "确定",
      cancelButtonText: "取消"
    }
  )
    .then(async () => {
      const operateLog = {
        operateLogType: "TEACHER_DATABASE",
        operateType: `发布了“${course.value.join("、")}"课期`
      };
      let [err, res] = await to(deletIds({ ids: ids.value }, operateLog));
      if (res.code === 200 && res.data.length === 0) {
        ElMessage.success("发布成功");
        batchDeleteArr.value = [];
        ids.value = [];
        course.value = [];
        tableRef.value.clearSelection();
        getTablePeriodList();
      } else {
        let coursePeriodName = [];
        let reason = [];
        res.data.forEach(item => {
          coursePeriodName.push(item.coursePeriodName);
          reason.push(item.reason);
        });
        // ElMessage.error(`删除失败,${res.msg}`);
        ElMessageBox.confirm(
          `${coursePeriodName.map((item, i) => `${item}:${reason[i]}`).join("、")}`,
          "发布失败",
          {
            cancelButtonText: "取消"
          }
        );
      }
      if (err) {
        ElMessage.error("发布失败");
      }
    })
    .catch(() => {});
};
onMounted(async () => {
  // getTableList();
  getTablePeriodList();
});
</script>

<template>
  <div class="containers">
    <div class="content_bottom">
      <div class="con_top">
        <div>请选择课期</div>
      </div>
      <div class="con_table">
        <el-table
          ref="tableRef"
          :data="tableData"
          table-layout="fixed"
          :header-cell-style="{ backgroundColor: '#fafafa', color: '#565353' }"
          highlight-current-row
          :height="
            route.query.type === 'teachers'
              ? 'calc(100vh - 29vh)'
              : 'calc(100vh - 33vh)'
          "
          width="100%"
          :row-key="getRowKeys"
          @selection-change="handleSelectionChange"
        >
          <!-- 添加单选列 -->
          <el-table-column
            v-if="route.query.type === 'teachers'"
            label="期号"
            width="100"
            align="right"
          >
            <template #default="{ row }">
              <el-radio
                v-model="selectedRowId"
                :label="String(row.termNumber)"
                :disabled="
                  (row.coursePeriodState !== 'NOT_LISTED' &&
                    dayjs().valueOf() > row.openTime) ||
                  (row.coursePeriodState !== 'OFFLINE' &&
                    row.isExistInformation === false &&
                    dayjs().valueOf() > row.openTime)
                "
                :class="row.termNumber >= 10 ? 'small' : 'big'"
                @change="handleSelectRow(row)"
              />
            </template>
          </el-table-column>
          <el-table-column
            v-if="route.query.type !== 'teachers'"
            :selectable="selectable"
            :reserve-selection="true"
            type="selection"
            width="55"
          />

          <el-table-column
            v-if="route.query.type !== 'teachers'"
            prop="termNumber"
            label="期号"
            width="80"
          >
            <template #default="scope">
              <el-text>
                {{ scope.row.termNumber || 0 }}
              </el-text>
            </template>
          </el-table-column>
          <el-table-column
            prop="name"
            label="课期名"
            align="left"
            show-overflow-tooltip
            width="150"
          >
            <template #default="scope">
              {{ scope.row?.name || "--" }}
            </template>
          </el-table-column>
          <el-table-column
            prop="openTime"
            label="开课时间"
            align="left"
            min-width="180"
          >
            <template #default="scope">
              <div>
                {{
                  formatTime(scope.row.openTime, "YYYY-MM-DD HH:mm:ss") || "--"
                }}
              </div>
            </template>
          </el-table-column>
          <el-table-column
            prop="createdAt"
            label="创建时间"
            align="left"
            min-width="180"
          >
            <template #default="scope">
              <div>
                {{
                  formatTime(scope.row.createdAt, "YYYY-MM-DD HH:mm:ss") || "--"
                }}
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="buyType" label="购买类型" align="left">
            <template #default="scope">
              <div>
                {{
                  scope.row.buyType === "ORDINARY"
                    ? "普通单"
                    : scope.row.buyType === "PRIVATE_DOMAIN_GROUP_ORDER"
                      ? "团购单"
                      : "--"
                }}
              </div>
            </template>
          </el-table-column>
          <el-table-column
            prop="leaders"
            label="领队"
            align="left"
            width="200"
            show-overflow-tooltip
          >
            <template #default="scope">
              <div>
                {{ getName(scope.row.leaders) || "--" }}
              </div>
            </template>
          </el-table-column>
          <el-table-column
            prop="lecturers"
            label="讲师"
            align="left"
            width="200"
            show-overflow-tooltip
          >
            <template #default="scope">
              <div>
                {{ getName(scope.row.lecturers) || "--" }}
              </div>
            </template>
          </el-table-column>
          <el-table-column
            prop="lecturers"
            label="专家"
            align="left"
            width="200"
            show-overflow-tooltip
          >
            <template #default="scope">
              <div>
                {{ getName(scope.row.lecturers) || "--" }}
              </div>
            </template>
          </el-table-column>
          <el-table-column
            width="120px"
            prop="coursePeriodState"
            label="课期状态"
          >
            <template #default="scope">
              <div
                v-if="
                  scope.row.offlineType === 'PLATFORM_OFFLINE' ||
                  scope.row.offlineType === 'PLATFORM_CLOSE_GROUP'
                "
              >
                <el-tooltip
                  class="box-item"
                  title=""
                  :content="
                    scope.row.offlineType === 'PLATFORM_OFFLINE'
                      ? '课期已被强制下架，请联系平台客服了解情况'
                      : '课期已被强制关闭团购，请联系平台客服了解情况'
                  "
                  placement="bottom"
                  effect="light"
                >
                  <div class="state-reject">
                    {{ getSatte(scope.row.coursePeriodState) || "--" }}
                    <el-icon style="color: red; margin-left: 3px">
                      <Warning />
                    </el-icon>
                  </div>
                </el-tooltip>
              </div>
              <div v-else class="state-reject">
                {{ getSatte(scope.row.coursePeriodState) || "--" }}
              </div>
            </template>
          </el-table-column>
          <el-table-column
            width="120px"
            prop="auditState"
            label="审核状态"
            align="left"
          >
            <template #default="scope">
              <div
                v-if="
                  scope.row.reviewState === 'OFFLINE_REJECT' ||
                  scope.row.reviewState === 'ONLINE_REJECT'
                "
              >
                <el-tooltip
                  class="box-item"
                  title=""
                  :content="scope.row.opinion ? scope.row.opinion : '无'"
                  placement="bottom"
                  effect="light"
                >
                  <div class="state-reject">
                    {{ AUDIT_ENUM[scope.row.reviewState]?.label || "无" }}
                    <el-icon style="color: red; margin-left: 3px">
                      <Warning />
                    </el-icon>
                  </div>
                </el-tooltip>
              </div>
              <div v-else class="state-reject">
                {{ AUDIT_ENUM[scope.row.reviewState]?.label || "无" }}
              </div>
            </template>
          </el-table-column>
          <el-table-column
            prop="address"
            fixed="right"
            label="操作"
            align="left"
          >
            <template #default="{ row }">
              <el-link
                v-if="
                  (row.coursePeriodState === 'NOT_LISTED' &&
                    dayjs().valueOf() < row.openTime) ||
                  (row.coursePeriodState === 'OFFLINE' &&
                    row.isExistInformation === false &&
                    dayjs().valueOf() < row.openTime)
                "
                type="primary"
                :underline="false"
                @click="detailEvt(row)"
              >
                详情
              </el-link>
              <el-link v-else type="info" :underline="false" disabled>
                详情
              </el-link>
            </template>
          </el-table-column>
          <!-- 批量删除 -->
        </el-table>
        <div
          :class="
            route.query.type === 'teachers' ? 'con1_delete' : 'con_delete'
          "
        >
          <div class="delete-con">
            <div v-if="batchDeleteArr.length > 0">
              <span class="delete-tip">已选{{ batchDeleteArr.length }}个课期：</span>
              <span
                v-for="item in batchDeleteArr"
                :key="item.id"
                class="delete-tip"
                >{{ item.name }}&nbsp;&nbsp;</span>
            </div>
          </div>
          <!-- <el-button
            v-if="route.query.type === 'teachers'"
            type="default"
            style="margin-right: 10px"
            @click="cancelEvt"
          >
            取消
          </el-button> -->
          <el-button
            v-if="route.query.type === 'teachers'"
            type="primary"
            @click="batchInvite"
          >
            确认邀请
          </el-button>
          <el-button v-else type="primary" @click="batchDelete">
            确认发布
          </el-button>
        </div>
      </div>
      <div class="con_pagination">
        <!-- 分页 -->
        <el-pagination
          v-model:current-page="params.page"
          v-model:page-size="params.size"
          class="pagination"
          background
          :page-sizes="[15, 30, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="params.totalElements"
          @current-change="handleCurrentChange"
          @size-change="handleSizeChange"
        />
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.containers {
  box-sizing: border-box;
  // width: calc(100% - 48px);
  height: 100%;
  background: #f0f2f5;

  .content_bottom {
    box-sizing: border-box;
    padding: 20px;
    // margin-top: 20px;
    background-color: #fff;
    .con_top {
      display: flex;
      align-items: center;
      justify-content: flex-start;
      width: 100%;
      height: fit-content;
      margin-bottom: 20px;
    }
  }

  :deep(.el-button + .el-button) {
    margin: 0;
  }
}

.state-reject {
  width: 40px;
  display: flex;
  align-items: center;
  white-space: nowrap;
}
:deep(.el-descriptions__label.el-descriptions__cell.is-bordered-label) {
  background: #e1f5ff;
}

.con_table {
  // margin-bottom: 24px;
  .option {
    display: flex;

    .btnse {
      display: flex;
      margin-right: 16px;
      color: #4095e5;
      cursor: pointer;
    }
  }
}

.con_pagination {
  display: flex;
  justify-content: flex-end;
  width: 100%;
}
.delete-tip {
  font-size: 14px;
  &:nth-of-type(1) {
    margin-left: 10px;
  }
}
.con_delete {
  margin-top: 10px;
  min-height: 50px;
  .delete-con {
    margin-bottom: 2px;
    height: 40px;
    overflow-y: auto;
  }
}
.con1_delete {
  margin-top: 10px;
  // min-height: 50px;
  .delete-con {
    margin-bottom: 2px;
    overflow-y: auto;
  }
}
/* 单选按钮样式 */
:deep(.el-radio__input.is-checked .el-radio__inner) {
  border-color: #4095e5;
  background: #4095e5;
}
:deep(.el-radio__input.is-checked + .el-radio__label) {
  color: #4095e5;
}
:deep(.big .el-radio__label) {
  padding-left: 30px;
}
:deep(.small .el-radio__label) {
  padding-left: 22px;
}
:deep(.is-disabled) {
  background-color: transparent !important;
  opacity: 1 !important;
}
</style>
