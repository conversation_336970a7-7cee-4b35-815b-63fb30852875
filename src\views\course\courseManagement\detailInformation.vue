<script setup>
import { ref, onMounted } from "vue";
import { useRoute } from "vue-router";
import TabTitle from "@/components/Base/tabInfo.vue";
import AllEvalute from "./components/allEvaluate.vue";
import EvaluteContent from "@/components/course/evaluateContent.vue";
import { courseFindById } from "@/api/course.js";
import { requestTo } from "@/utils/http/tool";
import { formatTime } from "@/utils/index";

const route = useRoute();
const textarea = ref("");
const showContent = ref(true);

// 表头
const tableHeader = ref([
  {
    id: "1",
    label: "课程名",
    value: "--",
    width: "107px"
  },
  {
    id: "2",
    label: "课程ID",
    value: "--",
    width: "107px"
  },
  {
    id: "3",
    label: "创建时间",
    value: "--",
    width: "107px"
  },
  {
    id: "4",
    label: "课程类型",
    value: "--",
    width: "107px"
  },
  {
    id: "5",
    label: "人数限制",
    value: "--",
    width: "107px"
  },
  {
    id: "6",
    label: "机构",
    value: "--",
    width: "107px"
  }
  // {
  //   id: "7",
  //   label: "基地",
  //   value: "--",
  //   width: "107px"
  // }
]);
const url = ref();

const srcList = ref([]);
const tabTitle = ref([
  //   { id: 1, name: "行程安排" },
  //   { id: 2, name: "课程介绍" },
  //   { id: 3, name: "课程知识点" },
  //   { id: 4, name: "装备说明" },
  //   { id: 5, name: "注意事项" },
  //   { id: 6, name: "价格设置" },
  //   { id: 7, name: "作业设计" },
  { id: 8, name: "全部评价" }
]);
const tabInfoEvt = obj => {
  console.log("💗tabInfoEvt---------->", obj);
};
// 获取列表信息
const getListLoading = ref(false);
const getTableList = async data => {
  if (getListLoading.value) {
    return;
  }
  getListLoading.value = true;
  let paramsData = {
    id: route.query.id
  };
  const [err, result] = await requestTo(courseFindById(paramsData));
  if (result) {
    // console.log('🍪result-------3333----------------------->',result);
    tableHeader.value[0].value = result.name || "--";
    tableHeader.value[1].value = result.id || "--";
    tableHeader.value[2].value =
      formatTime(result.createdAt, "YYYY-MM-DD HH:mm:ss") || "--";
    tableHeader.value[3].value = result.courseType?.name || "--";
    if (result.minPeopleNumber && result.maxPeopleNumber) {
      tableHeader.value[4].value =
        result.minPeopleNumber + "-" + result.maxPeopleNumber;
    } else if (result.minPeopleNumber) {
      tableHeader.value[4].value = result.minPeopleNumber;
    } else if (result.maxPeopleNumber) {
      tableHeader.value[4].value = result.maxPeopleNumber;
    } else {
      tableHeader.value[4].value = "--";
    }
    if (result.files?.length) {
      result.files.map(item => {
        srcList.value.push(item?.uploadFile?.url);
      });
      url.value = result.files[0]?.uploadFile?.url;
    }
    tableHeader.value[5].value = result.organization?.name || "--";
    // tableHeader.value[6].value = result.complex?.name || "--";
  } else {
    ElMessage.error(err);
  }
  getListLoading.value = false;
};
const sendContentData = ref({});
const contentData = val => {
  console.log("💗evaluateData----33--444---->", val);
  sendContentData.value = val;
};
// 用户评价点击详情是否展示切换页组件
const evaluateTabShow = val => {
  showContent.value = val;
};
onMounted(() => {
  getTableList();
});
</script>

<template>
  <div class="examine-detail">
    <div class="curse-table">
      <el-descriptions
        class="margin-top"
        title=""
        :column="3"
        border
        style="width: 1500px"
      >
        <template v-for="(item, index) in tableHeader" :key="index">
          <el-descriptions-item
            width="120px"
            label-align="center"
            :span="item.label === '创建时间' ? 2 : ''"
          >
            <template #label>
              <div class="cell-item">{{ item.label }}</div>
            </template>
            {{ item.value }}
          </el-descriptions-item>
        </template>
      </el-descriptions>
      <div class="img">
        <el-image
          :src="url"
          :zoom-rate="1.2"
          :max-scale="7"
          :min-scale="0.2"
          :preview-src-list="srcList"
          show-progress
          :initial-index="4"
          fit="cover"
          class="img-pic"
        />
      </div>
    </div>
    <div v-if="showContent" class="info-table">
      <!-- tab切换 -->
      <TabTitle :tabTitle="tabTitle" @tab-data="tabInfoEvt" />
      <!-- 切换信息 -->
      <div class="tab-info">
        <AllEvalute
          :periodName="tableHeader[0].value"
          @tab-show="evaluateTabShow"
          @evaluate="contentData"
        />
      </div>
    </div>
    <!-- 内容信息 -->
    <div v-else class="info-detail">
      <EvaluteContent
        :evaluateData="sendContentData"
        @tab-show="evaluateTabShow"
      />
    </div>
  </div>
</template>

<style lang="scss" scoped>
.examine-detail {
  // width: calc(100% - 48px);
  // height: calc(100vh - 48px);
  // height: calc(100vh - 150px);
  // overflow-y: auto;
  .curse-table {
    box-sizing: border-box;
    display: flex;
    justify-content: space-between;
    width: 100%;
    padding: 24px 20px;
    // height: 250px;
    margin-bottom: 20px;
    // width: calc(100% - 48px);
    background-color: #fff;

    .img {
      width: 145px;
      height: 80px;
      margin-left: 30px;

      .img-pic {
        width: 145px;
        height: 80px;
        // object-fit: cover;
      }
    }
  }

  .info-table {
    width: 100%;
    height: 436px;

    .tab-info {
      box-sizing: border-box;
      width: 100%;
      height: 560px;
      padding: 30px;
      background-color: #fff;
      // border-top:2px solid red;
    }
  }
  .info-detail {
    width: 100%;
    height: 560px;
    background-color: #fff;
  }
}

:deep(.el-descriptions__label.el-descriptions__cell.is-bordered-label) {
  background: #e1f5ff;
}
</style>
