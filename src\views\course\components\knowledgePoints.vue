<script setup>
import { onMounted, ref, watch, computed, nextTick } from "vue";
import { PlusTable, useTable, PlusDialogForm } from "plus-pro-components";
import { useRoute, useRouter } from "vue-router";
import {
  nowledgefindById,
  nowledgeCreateOrUpdate,
  findCourseKnowledgePointByGrade,
  findCourseKnowledgePointBySubject,
  findCourseMaterialVersion,
  findCourseDataCourse,
  findCourseDataCourseBySubject,
  findCourseKnowledgePoint,
  saveDraftCourseKnowledge,
  // findCourseKnowledgePointByParentId,
  deleteCourseKnowledgePoint2,
  findById2,
  nowledgeCreateOrUpdate2
} from "@/api/period.js";
import {
  draftCourseKnowledgePointFindByDraftId,
  draftCourseKnowledgePoint,
  saveDraftCourseKnowledgePointNext,
  findByIdDraftCourseKnowledgePoint,
  deleteDraftCourseKnowledgePoint,
  draftDelete
} from "@/api/drafts.js";
// import RichEditor from "@/components/Base/RichEditor.vue";
import { requestTo } from "@/utils/http/tool";
// import { number } from "echarts";
import { ElMessage, ElMessageBox } from "element-plus";
// import DescriptionList from "./descriptionList.vue";
import { to, deepClone } from "@iceywu/utils";
import { aiNewPage } from "@/utils/aiTool.js";
import { courseStore } from "@/store/modules/course.js";

const props = defineProps({
  infoShow: {
    type: String,
    default: ""
  },
  draftId: {
    type: Number,
    default: 0
  },
  periodName: {
    type: String,
    default: "未命名"
  },
  infoShowEnName: {
    type: String,
    default: "foundation"
  },
  multiple: {
    type: Boolean, // 使用构造函数
    default: false
  }
});

const emites = defineEmits(["baseInfo"]);

const materialCache = new Map();

onMounted(() => {
  findCourseKnowledgePointByGradeList();
  findCourseKnowledgePointBySubjectList();
  // findCourseKnowledgePointByCityList();
});
const useCourseStore = courseStore();
const router = useRouter();
const route = useRoute();
const valueHtml = ref();
const operateLog = ref({});
// 判断api类型
const getApiType = (val, isEdit) => {
  let res = "";
  // case "课期知识点":
  res = isEdit ? nowledgeCreateOrUpdate : nowledgefindById;
  operateLog.value = {
    operateLogType: "COURSE_MANAGEMENT",
    operateType: `编辑了“${props.periodName}”课期中的课程知识点`
  };
  //   break;
  return res;
};
const operateLogDraft = ref({});
// 判断api类型草稿箱
const getApiDraftType = (val, isEdit, obj) => {
  let res = "";
  res = isEdit
    ? draftCourseKnowledgePoint
    : draftCourseKnowledgePointFindByDraftId;
  if (obj) {
    operateLogDraft.value = obj;
  } else {
    operateLogDraft.value = {
      operateLogType: "COURSE_MANAGEMENT",
      operateType: `将“${props.periodName}”课期中的课期知识点保存在草稿箱`
    };
  }
  return res;
};
// 判断日志输出
const getLogType = val => {
  let res = {};
  // case "课期知识点":
  res = {
    operateLogType: "COURSE_MANAGEMENT",
    operateType: `编辑了“${props.periodName}”课期中的课程知识点`
  };
  return res;
};

const valueHtmlClone = ref("");
const tableData2 = ref([]);
// 查询详情
const getIntroductionfindById = async () => {
  const params = {
    coursePeriodId: Number(route.query.periodId)
  };
  let api = getApiType(props.infoShow, false);
  let [err, res] = await to(api(params));
  if (res.code === 200) {
    // valueHtml.value = res?.data?.content || "";
    // valueHtmlClone.value = deepClone(valueHtml.value);
    // tableData2.value = [...res.data];
    tableData.value = await tableDataInit(tableData.value, res.data);
  } else {
    console.log("🐬-----err-----", res.msg);
  }
  if (err) {
    console.log("🐬-----err-----", err);
  }
};
// 草稿箱查询详情
const getdraftfindById = async () => {
  const params = {
    draftId: Number(route.query.draftId) || Number(useCourseStore.draftId)
  };
  let api = getApiDraftType(props.infoShow, false);

  let [err, res] = await to(api(params));
  if (res.code === 200) {
    // tableData2.value = [...res.data];
    tableData.value = await tableDataInit(tableData.value, res.data);
  } else {
    console.log("🐬-----err-----", res.msg);
  }
  if (err) {
    console.log("🐬-----err-----", err);
  }
};
// 中间函数处理页面数据回显
const tableDataInit = async (obj, objRes) => {
  obj = objRes;
  obj.map(async (item, i) => {
    values.value.stageId = item.stage?.id;
    values.value.subjectId = item.subject?.id;
    item.stage = item.stage?.name;
    item.subject = item.subject?.name;
    item.switch =
      item.textbook?.name && item.catalog?.name
        ? `${item.textbook?.name} > ${item.catalog?.name}`
        : "";
    item.knowledge = item.knowledge?.name;
    item.content = processContent(item.content);
    item.abilities = item.abilities.map(i => i.abilityDict.name).join("，");
  });

  return obj;
};
const submitLoading = ref(false);
// 保存
const submitForm = async val => {
  if (submitLoading.value) return;
  submitLoading.value = true;
  await saveApi(val);
  submitLoading.value = false;
};
const submitBackLoading = ref(false);
// 保存并返回
const submitFormBack = async val => {
  if (submitBackLoading.value) return;
  submitBackLoading.value = true;
  await saveApi(val);
  submitBackLoading.value = false;
};

// 保存api(编辑)
const saveApi = async (val, obj) => {
  // const params = {
  //   coursePeriodId: Number(route.query.periodId),
  //   knowledgePoints: []
  // };
  if (tableData.value.length === 0) {
    ElMessage({
      type: "error",
      message: "请输入内容"
    });
    return;
  }
  // operateLog.value = getLogType(props.infoShow);
  // let api = getApiType(props.infoShow, true);
  // let [err, res] = await requestTo(api(params, operateLog.value));
  // if (res) {
  if (val) {
    if (route.query.fromPage === "courseDetail") {
      router.replace({
        path: "/course/courseDetails",
        query: { id: route.query.courseId }
      });
    } else if (route.query.fromPage === "currentDetail") {
      router.replace({
        path: "/course/courseDetails/currentDetails",
        query: {
          periodId: route.query.periodId,
          courseId: route.query.courseId
        }
      });
    }
  }
  ElMessage.success("保存成功");
  // } else {
  //   ElMessage.error("保存失败");
  //   console.log("🐬-----err-----", err);
  // }
};
const draftLoading = ref(false);
const saveDraftApi = async () => {
  const params = {
    draftId: Number(route.query.draftId) || Number(useCourseStore.draftId),
    content: valueHtml.value
  };

  let api = getApiDraftType(props.infoShow, true);
  let [err, res] = await to(api(params, operateLog.value));
  if (res.code === 200) {
    // console.log("🐠res--------保存草稿箱---------------------->", res);
    ElMessage.success("当前资料已保存到草稿箱");
  } else {
    ElMessage.error("保存失败", res.msg);
    console.log("🐬-----err-----", res.msg);

    if (err) {
      console.log("🐬-----err-----", res.msg);
    }
  }
};
// 保存草稿箱
const submitDraftForm = async () => {
  if (draftLoading.value) return;
  draftLoading.value = true;
  if (tableData.value.length <= 0) {
    ElMessage({
      type: "error",
      message: "请添加知识点"
    });
    draftLoading.value = false;
    return;
  } else {
    ElMessage.success("当前资料已保存到草稿箱");
  }
  // else {
  //   await saveDraftApi();
  // }

  draftLoading.value = false;
};
// 上一步
const lastSubmitForm = () => {
  if (tableData.value.length <= 0) {
    saveDraftApi();
  }
  let info = {
    infoShow: "",
    draftId: Number(route.query.draftId) || Number(useCourseStore.draftId),
    periodName: props.periodName
  };
  if (props.infoShow === "课期知识点") {
    info.infoShow = "课期介绍";
  }
  emites("baseInfo", info);
};
const nextSubmitLoading = ref(false);
// 下一步
const nextSubmitForm = async () => {
  if (nextSubmitLoading.value) return;
  nextSubmitLoading.value = true;

  let info = {
    infoShow: "",
    draftId: Number(route.query.draftId) || Number(useCourseStore.draftId),
    complete: true,
    periodName: props.periodName
  };
  if (props.infoShow === "课期知识点") {
    info.infoShow = "作业设计";
  }
  emites("baseInfo", info);
  nextSubmitLoading.value = false;
};
const btnData = ref([
  { id: 1, name: "放弃新建" },
  { id: 2, name: "保存到草稿箱" }
]);
const deleteDraft = async () => {
  let operateLogDraft = {
    operateLogType: "COURSE_MANAGEMENT",
    operateType: route.query.draftId
      ? `删除了草稿id为“${route.query.draftId}”的草稿数据`
      : `删除了草稿id为“${useCourseStore.draftId}”的草稿数据`
  };
  const [err, res] = await to(
    draftDelete(
      { id: Number(route.query.draftId) || Number(useCourseStore.draftId) },
      operateLogDraft
    )
  );
  if (res.code === 200) {
    ElMessage.success("删除成功");
  } else {
    ElMessage.error("删除失败");
  }
};
// 退出
const backEvt = (val, it) => {
  if (val === "exit") {
    let titlt =
      it.id === 1
        ? `请确认是否清除当前编辑和提交的所有资料，并删除当前编辑资料在草稿箱中对应的草稿条目`
        : `请确认是否将当前编辑和提交的所有资料保存到草稿箱，并退出编辑页面`;
    ElMessageBox.confirm(`${titlt}`, `退出并${it.name}`, {
      confirmButtonText: "确定",
      cancelButtonText: "取消"
    }).then(() => {
      if (it.id === 1) {
        deleteDraft();
        if (route.query.type === "draft") {
          router.replace("/course/drafts");
        } else if (route.query.type === "edite") {
          router.replace("/course/courseDetails");
        } else {
          router.go(-1);
        }
      } else if (it.id === 2) {
        saveDraftApi();
        if (route.query.type === "draft") {
          router.replace("/course/drafts");
        } else if (route.query.type === "edite") {
          router.replace("/course/courseDetails");
        } else {
          router.go(-1);
        }
      }
    });
  } else {
    if (route.query.fromPage === "courseDetail") {
      router.replace({
        path: "/course/courseDetails",
        query: { id: route.query.courseId }
      });
    } else if (route.query.fromPage === "currentDetail") {
      router.replace({
        path: "/course/courseDetails/currentDetails",
        query: {
          periodId: route.query.periodId,
          courseId: route.query.courseId
        }
      });
    }
  }
};

onMounted(() => {
  // console.log(route.query.type);
  // if (route.query.type === "edite") {
  //   getIntroductionfindById();
  // } else {
  //   getdraftfindById();
  // }
});
watch(
  () => props.infoShow,
  () => {
    // console.log(route.query);
    if (route.query.type === "edite") {
      getIntroductionfindById();
    } else {
      getdraftfindById();
    }
  },
  { immediate: true },
  { deep: true }
);

// const submitDraftFormatt = async () => {
//   const tre = ref(false);
//   nextSubmitLoading.value = true;
//   if (
//     (props.infoShow === "课期介绍" && valueHtml.value === "<p><br></p>") ||
//     (props.infoShow === "用户协议" && valueHtml.value === "<p><br></p>")
//   ) {
//     ElMessage({
//       type: "error",
//       message: "请输入内容"
//     });
//     tre.value = true;
//     nextSubmitLoading.value = false;
//     return tre.value;
//   }
//   // if(valueHtmlClone.value === valueHtml.value){
//   //   tre.value = true;
//   //   nextSubmitLoading.value = false;
//   //   return tre.value;
//   // }
//   const params = {
//     draftId: Number(route.query.draftId) || Number(useCourseStore.draftId),
//     content: valueHtml.value
//   };
//   if (props.infoShow === "课期介绍") {
//     let operateLogDraft = {
//       operateLogType: "COURSE_MANAGEMENT",
//       operateType: `将“${props.periodName}”课期中的课期介绍保存在草稿箱`
//     };
//     let [err, res] = await to(
//       saveCourseIntroductionNext(params, operateLogDraft)
//     );
//     if (res.code === 200) {
//       ElMessage.success("当前资料已保存到草稿箱");
//       tre.value = true;
//     }
//   } else if (props.infoShow === "用户协议") {
//     let operateLogDraft = {
//       operateLogType: "COURSE_MANAGEMENT",
//       operateType: `将“${props.periodName}”课期中的用户协议保存在草稿箱`
//     };
//     let [err, res] = await to(
//       saveDraftUserAgreementNext(params, operateLogDraft)
//     );
//     if (res.code === 200) {
//       ElMessage.success("当前资料已保存到草稿箱");
//       tre.value = true;
//     }
//   } else {
//     if (valueHtml.value !== "<p><br></p>") {
//       const params = {
//         draftId: Number(route.query.draftId) || Number(useCourseStore.draftId),
//         content: valueHtml.value
//       };
//       let api = getApiDraftType(props.infoShow, true);
//       let [err, res] = await to(api(params, operateLog.value));
//       if (res.code === 200) {
//         ElMessage.success("当前资料已保存到草稿箱");
//         tre.value = true;
//       } else {
//         ElMessage.error("保存失败", res.msg);
//         tre.value = false;
//       }
//     }
//   }
//   nextSubmitLoading.value = false;
//   return tre.value;
// };

// 用于草稿箱点击课期介绍校验
defineExpose({
  submitDraftForm
});
// console.log("---------------------------------------------------------------");

const confirmLoading = ref(false);
const { tableData, buttons, total, pageInfo } = useTable();
const tableConfig = [
  {
    label: "学段",
    prop: "stage",
    width: "100"
  },
  {
    label: "学科",
    prop: "subject",
    width: "100"
  },
  {
    label: "教材关联",
    prop: "switch",
    width: "300"
  },
  {
    label: "核心知识点",
    prop: "knowledge",
    width: "200"
  },
  {
    label: "预期目标",
    prop: "content",
    width: "400"
  },
  {
    label: "提升能力",
    prop: "abilities",
    width: "200"
  }
];
const findTab = ref(false);
const initialValues = {
  id: "",
  content: "",
  subjectId: "",
  stageId: "",
  textbookId: "",
  switch: [],
  catalogId: [],
  knowledgeId: "",
  abilities: []
};
const values = ref({ ...initialValues });
const itemId = ref(null);
const findVal = ref([]);
buttons.value = [
  {
    // 编辑
    text: "编辑",
    code: "edit",
    props: {
      type: "primary"
    },
    show: computed(() => true),
    onClick: async params => {
      itemId.value = params.row.id;
      findVal.value = [];
      // console.log('🦄-----params-----', params);
      // 编辑回显
      let res;
      if (route.query.type === "edite") {
        res = await to(findById2({ id: params.row.id }));
        // findVal.value = res.data;
      } else {
        res = await to(
          findByIdDraftCourseKnowledgePoint({ id: params.row.id })
        );
        // findVal.value = res.data;
      }

      findVal.value = res[1].data;
      // console.log(res, "res.data-----------------------");
      if (res[1].code === 200) {
        values.value.stageId = findVal.value?.stage?.id;
        values.value.subjectId = findVal.value?.subject?.id;
        await Promise.all([
          findCourseMaterialVersionList(), // 材料关联 options
          findCourseKnowledgePointByTimeList(), // 核心知识点 options
          findCourseKnowledgePointByCityList() // 能力提升 options
        ]);
        values.value.content = findVal.value?.content;

        // 材料关联回显 - 路径数组
        if (findVal.value?.textbook && findVal.value?.catalog) {
          values.value.switch = findCascaderPath(
            columns.value[2].options,
            findVal.value.catalog.id,
            values.value.switch
          );
        } else {
          values.value.switch = [];
        }

        // 核心知识点回显 - 路径数组
        if (findVal.value?.knowledge) {
          values.value.knowledgeId = findCascaderPath(
            columns.value[3].options,
            findVal.value.knowledge.id,
            values.value.knowledgeId
          );
        } else {
          values.value.knowledgeId = [];
        }

        // 能力提升回显
        if (Array.isArray(findVal.value.abilities)) {
          const abilityPaths = findVal.value.abilities.map(item => {
            const path = findPathByValue(
              columns.value[5].options,
              item.abilityDict?.id || item.id
            );

            return path;
          });
          values.value.abilities = abilityPaths.filter(path => path.length > 0);
        } else {
          values.value.abilities = [];
        }
      }
      // console.log("🐬-----values.value-----", values.value);
      visible.value = true;
      findTab.value = true;
    }
  },
  {
    // 删除
    text: "删除",
    code: "delete",
    props: {
      type: "danger"
    },
    confirm: {
      title: "确认删除",
      message: "确定要删除此记录吗？"
    },
    onConfirm: async params => {
      // console.log(params.row);
      // return;
      const operateLog = {
        operateLogType: "COURSE_MANAGEMENT",
        operateType: `删除了“${props.periodName}”课期中的一个知识点`
      };
      // let id = { id: params.row.id };
      // console.log("🐬-----id-----", id);
      // return;
      if (route.query.type === "edite") {
        let [err, res] = await to(
          deleteCourseKnowledgePoint2({ id: params.row.id }, operateLog)
        );
        if (res.code === 200) {
          ElMessage.success("删除成功");
          getIntroductionfindById();
        } else {
          ElMessage.error("删除失败", res.msg);
        }
      } else {
        let [err, res] = await to(
          deleteDraftCourseKnowledgePoint({ id: params.row.id }, operateLog)
        );
        if (res.code === 200) {
          ElMessage.success("删除成功");
          getdraftfindById();
        } else {
          ElMessage.error("删除失败", res.msg);
        }
      }
    }
  }
];
function findPathByValue(options, targetId) {
  for (const node of options) {
    if (String(node.value) === String(targetId)) return [node.value];
    if (node.children) {
      const childPath = findPathByValue(node.children, targetId);
      if (childPath.length) return [node.value, ...childPath];
    }
  }
  return [];
}
// 表单操作
const visible = ref(false);
const dialogKey = ref(0);
const addCourseKnowledgePoint = async () => {
  // 重置表单数据
  values.value = deepClone(initialValues);

  // 清空缓存和 options
  materialCache.clear();
  columns.value[2].options = [];
  columns.value[3].options = [];
  columns.value[5].options = [];
  columns.value = [...columns.value];

  // 设置编辑状态
  findTab.value = false;

  // 强制重新渲染弹窗
  dialogKey.value++;

  // 打开弹窗
  visible.value = true;
};

const rules = {
  stageId: [
    {
      required: true,
      message: "请选择学段"
    }
  ],
  subjectId: [
    {
      required: true,
      message: "请选择学科"
    }
  ],
  switch: [
    {
      required: true,
      message: "请选择材料关联"
    }
  ],
  knowledgeId: [
    {
      required: true,
      message: "请选择核心知识点"
    }
  ],
  content: [
    {
      required: true,
      message: "请选择预期目标"
    }
  ],
  abilities: [
    {
      required: true,
      message: "请选择能力提升"
    }
  ]
};
const tableList = ref({
  draftId: 0,
  coursePeriodId: 0,
  knowledgePoints: initialValues
});
const tableList2 = ref({});
// 表单提交
// findTab判断编辑（true）还是新增（false）
const handleSubmit = async () => {
  confirmLoading.value = true;
  tableList.value.knowledgePoints = [];
  // console.log("values.value", values.value);
  const abilities = values.value.abilities.map(item => ({
    id: item[1],
    score: 1
  }));
  const operateLog = {
    operateLogType: "COURSE_MANAGEMENT",
    operateType: `${findTab.value === false ? "添加" : "编辑"}了“${props.periodName}”课期中的课程知识点`
  };
  // console.log("🐠-----findTab.value-----", findTab.value);
  if (findTab.value === false) {
    tableList.value.draftId =
      Number(route.query.draftId) || Number(useCourseStore.draftId);
    // console.log("🍪-----useCourseStore-----", useCourseStore);
    tableList.value.coursePeriodId = Number(route.query.periodId)
      ? Number(route.query.periodId)
      : tableList.value.draftId;
    // console.log("🌳-----route.query-----", route.query);
    tableList.value.knowledgePoints[0] = {
      content: values.value.content,
      subjectId: values.value.subjectId,
      stageId: values.value.stageId,
      textbookId: values.value?.switch[1],
      catalogId: values.value?.switch.slice(-1)[0],
      knowledgeId: values.value?.knowledgeId.slice(-1)[0],
      abilities: [...abilities]
    };
    console.log(values.value?.switch);
    // 判断是草稿箱还是课程
    if (route.query.type === "edite") {
      tableList.value.coursePeriodId = Number(route.query.periodId);
      const [err, res] = await to(
        saveDraftCourseKnowledge(tableList.value, operateLog)
      );
      if (res.code === 200) {
        ElMessage.success("当前资料已保存");
        getIntroductionfindById();
      } else {
        ElMessage.error("保存失败", res.msg);
      }
    } else {
      const [err, res] = await to(
        saveDraftCourseKnowledgePointNext(tableList.value, operateLog)
      );
      if (res.code === 200) {
        ElMessage.success("当前资料已保存");
        getdraftfindById();
      } else {
        ElMessage.error("保存失败", res.msg);
      }
    }
  } else {
    tableList2.value = {
      id: itemId.value,
      content: values.value.content,
      subjectId: values.value.subjectId,
      stageId: values.value.stageId,
      textbookId: values.value?.switch[1],
      catalogId: values.value?.switch[3] || values.value?.switch[2],
      knowledgeId: values.value?.knowledgeId[1],
      abilities: [...abilities]
    };
    if (route.query.type === "edite") {
      let [err, res] = await to(
        nowledgeCreateOrUpdate2(tableList2.value, operateLog)
      );
      if (res.code === 200) {
        ElMessage.success("当前资料已保存");
        getIntroductionfindById();
      } else {
        ElMessage.error("保存失败", res.msg);
      }
      // console.log("🐳-----err, res-----", err, res);
    } else {
      let api = getApiDraftType(props.infoShow, true, operateLog);
      let [err, res] = await to(api(tableList2.value));
      if (res.code === 200) {
        ElMessage.success("当前资料已保存");
        getdraftfindById();
      } else {
        ElMessage.error("保存失败", res.msg);
      }
      // console.log("🐳-----err, res-----", err, res);
    }
  }

  setTimeout(() => {
    confirmLoading.value = false;
    visible.value = false;
  }, 2000);
};
// 表单change
// const handleChange = () => {
//   if (columns.value[2].options.length > 0) {
//     return;
//   }
//   // if (values.value.stageId && values.value.subjectId) {
//   //   findCourseMaterialVersionList();
//   //   findCourseKnowledgePointByTimeList();
//   //   findCourseKnowledgePointByCityList();
//   // }
// };

// 表单点击
const handleClick = () => {
  if (values.value.stageId && values.value.subjectId) {
    findCourseMaterialVersionList();
    findCourseKnowledgePointByTimeList();
    // findCourseKnowledgePointByCityList();
  }
};

const columns = ref([
  {
    label: "学段",
    // width: 50,
    prop: "stageId",
    valueType: "select",
    options: []
  },
  {
    label: "学科",
    // width: 120,
    prop: "subjectId",
    valueType: "select",
    options: []
  },
  {
    label: "材料关联",
    // width: 100,
    prop: "switch",
    valueType: "cascader",
    options: []
  },
  {
    label: "核心知识点",
    prop: "knowledgeId",
    valueType: "cascader",
    options: []
  },
  {
    label: "预期目标",
    prop: "content",
    valueType: "textarea"
  },
  {
    label: "能力提升",
    prop: "abilities",
    valueType: "cascader",
    options: [],
    props: { multiple: true, checkStrictly: true }
  }
]);
// 学段查询
const findCourseKnowledgePointByGradeList = async () => {
  const [err, res] = await to(findCourseKnowledgePointByGrade());
  if (!res.data) return;
  columns.value[0].options = res.data.map(item => {
    return {
      label: item.name,
      value: item.id
    };
  });
};
// 学科查询
const findCourseKnowledgePointBySubjectList = async () => {
  const [err, res] = await to(findCourseKnowledgePointBySubject());
  if (!res.data) return;
  columns.value[1].options = res.data.map(item => {
    return {
      label: item.name,
      value: item.id
    };
  });
};
// 关联材料查询（版本查询）
const findCourseMaterialVersionList = async () => {
  if (values.value.stageId && values.value.subjectId) {
    const [err, res] = await to(
      findCourseMaterialVersion({
        stageId: values.value.stageId,
        subjectId: values.value.subjectId
      })
    );
    if (!res.data) return;

    const versionOptions = await Promise.all(
      res.data.map(async version => {
        const textbooks = await Promise.all(
          (version.textbooks || []).map(async textbook => {
            const [err2, res2] = await to(
              findCourseDataCourse({ textbookId: textbook.id })
            );
            let children = [];
            if (res2 && res2.data) {
              children = res2.data.map(child => ({
                label: child.name,
                value: child.id,
                children: selectList(child.children)
              }));
            }
            return {
              label: textbook.volume,
              value: textbook.id,
              children
            };
          })
        );
        return {
          label: version.name,
          value: version.id,
          children: textbooks
        };
      })
    );
    columns.value[2].options = versionOptions;
    columns.value = [...columns.value]; // 触发响应式
    // console.log("🦄-----columns.value-----", columns.value[2].options);
  } else {
    ElMessage.error("请先选择学段和学科");
    return;
  }
};
const selectList = val => {
  if (!val) return;
  let obj = [];
  val.map(item => {
    obj.push({
      label: item.volume ? item.volume : item.name,
      value: item.id,
      // parentId: item?.parentId || "",
      children:
        selectList(item.textbooks ? item.textbooks : item.children) || []
    });
  });
  return obj;
};
// 核心知识点查询
const findCourseKnowledgePointByTimeList = async () => {
  if (!values.value.stageId && !values.value.subjectId) return;
  const [err1, res1] = await to(
    findCourseKnowledgePoint({
      stageId: values.value.stageId,
      subjectId: values.value.subjectId
    })
  );
  const newOptions = selectList(res1.data);
  columns.value[3].options = newOptions;
};
// 能力提升
const findCourseKnowledgePointByCityList = async () => {
  // if (!values.value.stageId && !values.value.subjectId) return;
  const [err, res] = await to(
    findCourseDataCourseBySubject({
      parentId: 417
    })
  );
  if (!res.data) return;
  columns.value[5].options = selectList(res.data);
  // console.log('🍧-----columns.value[5].options-----', columns.value[5].options);
};
// 表单取消
const handleCancel = async () => {
  values.value = deepClone(initialValues);

  // 清空缓存
  materialCache.clear();

  // 重置 options
  columns.value[2].options = [];
  columns.value[3].options = [];
  columns.value[5].options = [];
  columns.value = [...columns.value];
};
// 表单回显
// const initValues = (option, id, values) => {
//   values = [];
//   let val = "";
//   option.map(i => {
//     if (i.value === id) {
//       if (i.children.length > 0) {
//         values.push(i.value);
//         initValues(i.children, values[values.length - 1], values);
//       }
//     } else {
//       if (i.children.length > 0) {
//         values.push(i.value);
//         initValues(i.children, values[values.length - 1], values);
//       }
//     }
//   });
//   console.log("🌈-----values-----", values);
//   return values;
// };

// ... existing code ...

/**
 * 根据目标ID在级联选项中查找完整路径
 * @param {Array} options - 级联选项数组
 * @param {string|number} targetId - 目标ID
 * @param {Array} currentPath - 当前路径（递归使用）
 * @returns {Array} 完整路径数组
 */
const findCascaderPath = (options, targetId, currentPath = []) => {
  for (const option of options) {
    const newPath = [...currentPath, option.value];

    // 找到目标节点
    if (String(option.value) === String(targetId)) {
      return newPath;
    }

    // 递归查找子节点
    if (option.children && option.children.length > 0) {
      const childPath = findCascaderPath(option.children, targetId, newPath);
      if (childPath.length > 0) {
        return childPath;
      }
    }
  }

  return [];
};
</script>

<template>
  <div class="introduction-edite">
    <div class="content_box">
      <el-button type="primary" @click="addCourseKnowledgePoint">
        添加课期知识点
      </el-button>
      <div class="content">
        <PlusTable
          :columns="tableConfig"
          :table-data="tableData"
          :action-bar="{
            buttons,
            width: '200px',
            align: 'center',
            fixed: 'right'
          }"
          :titleBar="false"
        />
      </div>
    </div>
    <div class="buttons">
      <div v-if="route.query.type === 'edite'" class="left">
        <el-button class="cancel" @click="backEvt('back')"> 返回 </el-button>
        <el-button
          type="primary"
          class="create"
          :loading="submitBackLoading"
          @click="submitFormBack(true)"
        >
          {{ "保存并返回" }}
        </el-button>
        <el-button
          type="primary"
          class="create"
          :loading="submitLoading"
          @click="submitForm(false)"
        >
          {{ "保存" }}
        </el-button>
      </div>
      <div v-else class="left">
        <!-- <el-button class="cancel" @click="backEvt('exit')"> 退出 </el-button> -->
        <el-dropdown>
          <el-button style="margin-right: 10px">退出</el-button>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item
                v-for="it in btnData"
                :key="it.id"
                @click="backEvt('exit', it)"
              >
                {{ it.name }}
              </el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
        <el-button
          type="primary"
          class="create"
          :loading="draftLoading"
          @click="submitDraftForm(ruleFormRef)"
        >
          {{ "保存草稿箱" }}
        </el-button>
        <el-button
          type="primary"
          class="create"
          :loading="submitLoading"
          @click="lastSubmitForm(ruleFormRef)"
        >
          {{ "上一步" }}
        </el-button>
        <el-button
          type="primary"
          class="create"
          :loading="nextSubmitLoading"
          @click="nextSubmitForm(ruleFormRef)"
        >
          {{ "下一步" }}
        </el-button>
      </div>
      <div class="right">
        <el-button
          type="primary"
          class="create"
          @click="aiNewPage(infoShowEnName)"
        >
          {{ "AI课程设计" }}
        </el-button>
      </div>
    </div>
    <!-- 表单弹窗 -->
    <PlusDialogForm
      :key="dialogKey"
      v-model:visible="visible"
      v-model="values"
      :form="{ columns, rules }"
      :dialog="{ title: '添加课程知识点', confirmLoading }"
      @confirm="handleSubmit(false)"
      @close="handleCancel"
      @click="handleClick"
    >
      <template #plus-field-abilities="{ options }">
        <el-cascader
          v-model="values.abilities"
          :options="options"
          :props="{ multiple: true }"
          clearable
        />
      </template>
      <template #plus-field-knowledgeId="{ options }">
        <el-cascader
          v-model="values.knowledgeId"
          :options="options"
          clearable
          :show-all-levels="false"
        />
      </template>
    </PlusDialogForm>
  </div>
</template>

<style lang="scss" scoped>
.introduction-edite {
  box-sizing: border-box;
  width: 100%;
  height: 100%;
  position: relative;
  padding: 10px 0 0 0;
  background-color: #fff;
  display: flex;
  .content_box {
    width: calc(100% - 100px);
    height: calc(100% - 40px);
    overflow-y: auto;
  }
  .buttons {
    display: flex;
    width: 100%;
    position: absolute;
    bottom: 0;
    left: 0;
    justify-content: space-between;
    padding-right: 80px;
  }
}
.content {
  //   margin-top: 15px;
  width: 100%;
  padding: 15px 0;
  //   height: 100%;
  //   background-color: red;
}
:deep(.w-e-text-placeholder) {
  top: 6px;
  left: 14px;
}
:deep(.el-button:focus-visible) {
  display: none;
}
:deep(.el-form-item__label) {
  width: 100px !important;
}
:deep(.el-input__wrapper) {
  width: 667px;
}
:deep(.el-link__inner) {
  margin: 5px;
}
:deep(.el-link::after) {
  border: none !important;
}
</style>
