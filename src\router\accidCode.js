// 首页
export const hoCodesList = {
  baseCode: 100, // 首页
  pending: 101
};
// 机构
export const inCodesList = {
  baseCode: 200, // 机构
  organizationalManagement: 201, // 机构管理
  institutionalReview: 202, // 机构审核
  baseManagement: 203, // 基地管理
  baseAudit: 204 // 基地审核
};
// 课程
export const coCodesList = {
  baseCode: 300, // 课程
  course: 301, // 课程管理
  classification: 302, // 分类管理
  examine: 303, // 课程审核
  order: 304, // 订单管理
  courseMarket: 305 // 课程市场
};
// 账号
export const acCodesList = {
  baseCode: 400, // 账号
  leader: 401, //领队管理
  lecturer: 402, //讲师管理
  parent: 403, //家长管理
  student: 404, //学生管理
  Localend: 405, //局端账号
  platform: 406, //平台账号
  roles: 407, //角色管理
  permission: 408 //权限管理
  // 按钮级
  // btn: {
  //     save: 801,
  // }
};
// 平台
export const plCodesList = {
  baseCode: 500, // 平台
  plPlatform: 501, //平台设置
  Log: 502, //日志管理
  expertDatabase: 503, //专家库
  find: 504, //发现管理
  analysis: 505, //数据分析
  knowledgeBase: 506, //知识库
  teacherResource: 507, //师资库
  facultyReview: 508, //师资审核
  news: 509 //新闻管理
};

// 其余子页面
export const reCodesList = {
  localendAccountAdd: 604, //局端账号创建
  platformAccountDetail: 605, //平台账号详情
  platformAccountAdd: 606, //平台账号创建
  courseExamineDetail: 607, //审核详情
  courseManagementDetail: 608, //课程详情
  courseManagementInformation: 609, //详细资料
  courseManagementCurrentDetails: 610, //作业情况
  parentManagementCompontsParentOrderManage: 611, //订单管理
  parentManagementCompontsParentDetails: 612, //家长管理详情
  parentManagementCompontsChildrenDetails: 613, //子女详情
  studentManagementCompontsStudentDetails: 614, //学生详情
  baseAdd: 615, //机构-新建机构
  baseEdit: 616, //机构-机构编辑
  accounting: 617, //机构-财务
  baseDetails: 618, //基地管理-详情
  teacherDetails: 619, //领队、讲师-详情
  qualificationsFile: 620, //资质文件
  editInformation: 621, //详情-编辑信息
  orderDetails: 622, //机构管理-账务
  baseExamineDtl: 623, //审核-详情
  institutionExamineDtl: 624, //审核
  courseOrderDetails: 625, //订单管理-订单详情
  homeEdit: 626, //编辑信息
  changePassword: 627, //修改密码
  findNewly: 627, //发现新增
  findDetails: 627, //发现详情
  findEdit: 627, //发现编辑
  bankAccount: 628, //银行账户
  courseMarketDetailInstitution: 629, //课程市场-机构发布详情
  courseMarketDetailFaculty: 630, //课程市场-师资发布详情
  courseMarketDetailRecruit: 631, //课程市场-招募详情
  courseMarketCurrentDetails: 632, //课程市场-课程详情,
  newsDetails: 633, //新闻详情
  newsEdit: 634, //新闻编辑
  newsNewly: 635 //新闻新增
};
