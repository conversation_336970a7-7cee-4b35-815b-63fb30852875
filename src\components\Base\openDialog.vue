<script setup>
import { id } from "element-plus/es/locale/index.mjs";
import { onMounted, ref, defineEmits } from "vue";

import router from "@/router/index";

import { requestTo } from "@/utils/http/tool";
const props = defineProps({
  title: {
    type: String
  },
  api: {
    type: Function,
    default: () => {}
  },
  id: {
    type: String
  },
  password: {
    type: String,
    default: ""
  },
  showContent: {
    type: String,
    default: "resetPassword"
  },
  rightBtnText: {
    type: String,
    default: "确定重置"
  }
});
const emit = defineEmits(["giveup", "close", "cancel"]);
onMounted(() => {
  console.log(props.title);
});

const getListLoading = ref(false);
const btnOKClick = async data => {
  if (getListLoading.value) {
    return;
  }
  getListLoading.value = true;
  let paramsData = {
    id: 100,
    password: props.password
  };
  if (props.showContent === "groupOrder") {
    console.log(
      "🌳-----import.meta.env.VITE_APP_SERVER_ID-----",
      import.meta.env.VITE_APP_SERVER_ID
    );

    // 开发环境
    if (Number(import.meta.env.VITE_APP_SERVER_ID) === 0) {
      paramsData = {
        coursePeriodId: props.id,
        qrCode: {
          scene: props.id,
          page: "pages/home/<USER>",
          checkPath: false,
          envVersion: "develop"
        }
      };
      // 正式环境
    } else {
      paramsData = {
        coursePeriodId: props.id,
        qrCode: {
          scene: props.id,
          page: "pages/home/<USER>"
        }
      };
    }
  }

  console.log("🍧-----paramsData-----", paramsData);
  // return
  const [err, result] = await requestTo(props.api(paramsData));
  console.log("🎁-----result-----", result);
  if (result) {
    if (props.showContent === "groupOrder") {
      router.push({
        path: "/course/currentDetails/groupOrder",
        query: { periodId: props.id, ordersId: result.ordersId }
      });
    }
  } else {
    ElMessage.error(err);
  }
  getListLoading.value = false;
  emit("close");
};
</script>

<template>
  <div class="popup">
    <div class="title">
      <span class="title_text">{{ title }} </span>
    </div>
    <div class="content">
      <div v-if="showContent === 'resetPassword'" class="info">
        <p>重置密码的账号 {{ "asn.zhang@.jiqoul" }}</p>
        <p>密码将被重置为 {{ "za212345@" }}</p>
        <p>密码生成规则：姓名首字母+手机号后6位+@</p>
      </div>
      <div v-if="showContent === 'groupOrder'" class="describe">
        <p>说明:</p>
        <p>1、一旦开启团购，该课程将无法在平台上进行公开展示，只</p>
        <p>能通过分享链接的方式让特定人群进行购买。</p>
        <p>2、一旦开启团购，该课程的所有信息将无法被修改。如需修</p>
        <p>改请关闭团购后再进行修改。</p>
        <p>3、如果一旦有用户付款下单且没有退货，则团购无法关闭。</p>
      </div>
      <div class="btns">
        <div class="btn_cancel" @click="$emit('cancel')">取消</div>
        <div class="btn_cancel" @click="btnOKClick">{{ rightBtnText }}</div>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.popup {
  width: 600px;
  height: 400px;
  background: #fff;

  .title {
    font-size: 14px;
    line-height: 40px;
    color: #fff;
    text-align: center;
    background: #333;
  }

  .content {
    .info {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      width: 100%;
      height: 300px;

      :nth-child(2) {
        margin: 20px 0 20px -46px;
      }
    }
    .describe {
      width: 65%;
      height: 190px;
      // border:1px solid red;
      margin: 53px auto;
      font-size: 14px;
    }

    .btns {
      display: flex;
      justify-content: space-between;
      padding: 0 40px;
      font-size: 14px;
      color: #fff;

      .btn_cancel {
        // padding: 8px 13px;
        width: 100px;
        height: 36px;
        cursor: pointer;
        background: #e6983a;
        border-radius: 8px;
        display: flex;
        justify-content: center;
        align-items: center;
      }

      :nth-child(2) {
        background: #4095e5;
      }
    }
  }
}
</style>
