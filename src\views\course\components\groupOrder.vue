<script setup>
import { ref, onMounted } from "vue";
import qrcode from "qrcode";
import { ElMessage, ElMessageBox } from "element-plus";
import {
  periodCloseGroupOrder,
  getQRCodeUrl,
  getGroupBuying
} from "@/api/course.js";
import { useRoute, useRouter } from "vue-router";
import useClipboard from "vue-clipboard3";
import DescriptionList from "./descriptionList.vue";
const { toClipboard } = useClipboard();

const route = useRoute();
const router = useRouter();
const refName = ref("团购分享");
const qrCodeData = ref("");
const url = ref("");
// 复制程序链接
const linkUrl = ref("");
onMounted(() => {
  // qrcode.toDataURL(url.value, (err, url) => {
  //   if (err) {
  //     console.error(err);
  //   } else {
  //     qrCodeData.value = url;
  //   }
  // });
  getQRCodeUrled();
});

// 根据课期id获取团购单小程序码url

const getQRCodeUrled = async () => {
  let params = {
    coursePeriodId: route.query.periodId
  };
  const { code, msg, data } = await getGroupBuying(params);
  if (code === 200 && data) {
    // console.log('🌵data------------------------------>',data);
    url.value = data?.qrCodeFileIdentifier;
    linkUrl.value = data?.scheme;
  } else {
    ElMessage({
      type: "error",
      message: `${msg} `
    });
  }
};

// 关闭团单

const closeOrder = async () => {
  ElMessageBox.confirm(`确定要关闭该团购课程吗`, "关闭团购", {
    confirmButtonText: "确定",
    cancelButtonText: "取消"
  })
    .then(() => {
      getClose();
    })
    .catch(() => {});
};
const periodName = ref("");
const periodNameEvt = val => {
  periodName.value = val;
};
const getClose = async () => {
  let params = {
    id: route.query.periodId
  };
  const operateLog = {
    operateLogType: "COURSE_MANAGEMENT",
    operateType: `关闭了“${periodName.value}”课期的团购`
    // operatorTarget: form.value.name,
  };
  const { code, msg } = await periodCloseGroupOrder(params, operateLog);
  if (code === 200) {
    router.replace({
      path: "/course/courseDetails/currentDetails",
      query: {
        periodId: route.query.periodId
      }
    });
  } else {
    ElMessage({
      type: "error",
      message: `${msg} `
    });
  }
};

const copyText = async content => {
  try {
    await toClipboard(linkUrl.value);
    ElMessage.success("复制成功");
  } catch (e) {
    ElMessage.error("复制失败,暂无链接");
  }
};
</script>

<template>
  <div>
    <DescriptionList
      :periodId="Number(route.query.periodId)"
      @name="periodNameEvt"
    />
    <div class="group-order">
      <div class="order_left">
        <div class="qrCode">
          <!-- <el-image :src="qrCodeData" style="height: 100%" /> -->
          <el-image
            :src="url"
            style="height: 100%"
            :hide-on-click-modal="true"
          />
        </div>
        <span> 用户使用微信扫描以上二维码即可查看课程内容并报名 </span>
      </div>
      <div class="order_right m_l">
        <div class="copyLink" @click="copyText">复制链接</div>
        <!-- <span class="tips">用户用微信打开以上链接即可查看课程内容并报名</span> -->
        <el-button class="close_order" type="primary" @click="closeOrder">
          关闭团购
        </el-button>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.group-order {
  width: 100%;
  // height: 100%;
  height: 650px;
  background: #fff;
  padding: 40px 30px 40px 60px;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  font-size: 12px;
  font-weight: bold;
}
.order_left {
  display: flex;
  flex-direction: column;
  align-items: center;
  height: 300px;
}
.order_right {
  display: flex;
  justify-content: flex-end;
  margin-top: 150px;
  // height: 300px;
}
.qrCode {
  width: 240px;
  height: 240px;
  background: #eee;
  margin-bottom: 20px;
}
.m_l {
  margin-left: 30px;
}
.oeder_links {
  width: 440px;
  min-height: 80px;
  border: 1px solid #eee;
  padding: 10px;
  box-sizing: border-box;
  white-space: pre-wrap;
  word-break: break-all;
  margin-right: 20px;
}
.copyLink {
  color: #409eff;
  cursor: pointer;
  display: flex;
  justify-content: flex-end;
}
.tips {
  width: 440px;
  text-align: center;
  margin-top: 20px;
  margin-right: 50px;
}
.close_order {
  position: absolute;
  bottom: 40px;
  right: 30px;
}
</style>
