<script setup>
import { ref, onMounted, computed } from "vue";
import { useRoute } from "vue-router";
import TabTitle from "@/components/Base/tabInfo.vue";
import Scheduling from "@/components/course/scheduling.vue";
import CourseReport from "@/components/course/courseReport.vue";
import PriceSetting from "@/components/course/priceSetting.vue";
import courseIntroduction from "@/components/course/courseIntroduction.vue";
import StudentSituation from "@/components/course/studentSituation.vue";
import JobDesign from "@/components/course/jobDesign.vue";
import UserEvaluate from "@/components/course/userEvaluate.vue";
import EvaluateContent from "@/components/course/evaluateContent.vue";
import HomeworkContent from "@/components/course/homeworkContent.vue";
import Order from "./components/relatedOrders.vue";
import ClassTrack from "@/components/course/classTrack.vue";
import { requestTo } from "@/utils/http/tool";
import dayjs from "dayjs";
import { findcoursePeriodId } from "@/api/course.js";
import {
  studentFindById,
  studentSituationFindById
} from "@/api/studentManage.js";
import { AUDIT_ENUM, COURSE_PERIOD_ENUM } from "@/utils/enum.js";
import { ImageThumbnail } from "@/utils/imageProxy";
import {
  Warning,
  ArrowDown,
  ArrowUp,
  Hide,
  View
} from "@element-plus/icons-vue";
import { decrypt } from "@/utils/SM4.js";
const route = useRoute();
const textarea = ref("");
const infoShow = ref("行程安排");
const contentShow = ref("homework");
const showTabContent = ref(true);
const coursePeroidInfo = ref({});

// 新增：控制展开/收起的状态
const isCollapsed = ref(false);

// 新增：切换展开/收起的方法
const toggleCollapse = () => {
  isCollapsed.value = !isCollapsed.value;
};

// 表头
const tableHeader = ref([
  {
    id: "1",
    label: "期号",
    value: "0",
    width: "107px"
  },
  {
    id: "2",
    label: "机构",
    value: "--",
    width: "107px"
  },
  {
    id: "3",
    label: "课期ID",
    value: "--",
    width: "107px"
  },
  {
    id: "4",
    label: "创建时间",
    value: "--",
    width: "107px"
  },
  {
    id: "5",
    label: "领队",
    value: "--",
    width: "107px"
  },
  {
    id: "6",
    label: "讲师",
    value: "--",
    width: "107px"
  },
  {
    id: "7",
    label: "开课时间",
    value: "--",
    width: "107px"
  },
  {
    id: "8",
    label: "截止报名",
    value: "--",
    width: "107px"
  },
  {
    id: "9",
    label: "基地",
    value: "--",
    width: "107px"
  },
  {
    id: "10",
    label: "购买类型",
    value: "--",
    width: "107px"
  },
  {
    id: "11",
    label: "课期状态",
    value: "--",
    width: "107px",
    state: ""
  },
  {
    id: "12",
    label: "审核状态",
    value: "--",
    width: "107px",
    state: "",
    opinion: ""
  }
]);

// 新增：计算属性，获取用于单行显示的表头 (前4项)
const firstRowHeaders = computed(() => tableHeader.value.slice(0, 4));

// 作业评价情况表头
const workHeader = ref([
  {
    id: "1",
    label: "学生ID",
    value: "--",
    width: "107px"
  },
  {
    id: "2",
    label: "学生姓名",
    value: "--",
    width: "107px"
  },
  {
    id: "3",
    label: "创建时间",
    value: "--",
    width: "107px"
  },
  {
    id: "4",
    label: "学校",
    value: "--",
    width: "107px"
  },
  {
    id: "5",
    label: "证件类型",
    value: "--",
    width: "107px"
  },
  {
    id: "6",
    label: "证件号",
    prop: "idNumber",
    value: "--",
    width: "107px"
  },
  {
    id: "7",
    label: "家长",
    value: "--",
    width: "107px"
  },
  {
    id: "8",
    label: "课程名",
    value: "--",
    width: "107px"
  },
  {
    id: "9",
    label: "上课时间",
    value: "--",
    width: "107px"
  }
]);

// 课程详情（家长学生管理）表头
const personHeader = ref([
  {
    id: "1",
    label: "课程名",
    value: "--",
    width: "107px"
  },
  {
    id: "2",
    label: "课期ID",
    value: "--",
    width: "107px"
  },
  {
    id: "3",
    label: "创建时间",
    value: "--",
    width: "107px"
  },
  {
    id: "4",
    label: "开课时间",
    value: "--",
    width: "107px"
  },
  {
    id: "5",
    label: "机构",
    value: "--",
    width: "107px"
  },
  {
    id: "6",
    label: "基地",
    value: "--",
    width: "107px"
  },
  {
    id: "7",
    label: "领队",
    value: "--",
    width: "107px"
  },
  {
    id: "8",
    label: "讲师",
    value: "--",
    width: "107px"
  },
  {
    id: "9",
    label: "购买价格",
    value: "--",
    width: "107px"
  },
  {
    id: "10",
    label: "购买规格",
    value: "--",
    width: "107px"
  },
  {
    id: "11",
    label: "获取学分",
    value: "--",
    width: "107px"
  },
  {
    id: "12",
    label: "参与人数",
    value: "--",
    width: "107px"
  }
]);

const tabTitle = ref([
  { id: 1, name: "行程安排" },
  { id: 2, name: "课期介绍" },
  { id: 3, name: "课期知识点" },
  { id: 4, name: "装备说明" },
  { id: 5, name: "注意事项" },
  { id: 6, name: "价格设置" },
  { id: 7, name: "作业设计" },
  { id: 8, name: "用户协议" },
  { id: 9, name: "学生情况" },
  { id: 10, name: "课堂跟踪" },
  { id: 11, name: "课期报告" },
  { id: 12, name: "用户评价" }
]);

const tabInfoEvt = obj => {
  // console.log("💗tabInfoEvt--22-------->", obj);
  infoShow.value = obj.name;
};

// 用户评价点击详情是否展示切换页组件
const evaluateTabShow = val => {
  showTabContent.value = val;
};

const targetTab = ref("");
const tabText = text => {
  targetTab.value = text;
};

const url = ref();

const srcList = ref([]);
const coursePeriodName = ref("");

// 查询课程课期详情
const getCoursePeriodFind = async () => {
  let [err, res] = await requestTo(
    findcoursePeriodId({ id: route.query.periodId })
  );
  if (res) {
    // console.log("🐬-----res1111-444----", res);
    if (route.query.text === "course") {
      coursePeroidInfo.value = res || {};
      coursePeriodName.value = res.name || "--";
      tableHeader.value[0].value = res.termNumber || "0";
      tableHeader.value[1].value = res.organization?.name || "--";
      tableHeader.value[2].value = res.id || "--";
      tableHeader.value[3].value =
        dayjs(res.createdAt).format("YYYY-MM-DD HH:mm:ss") || "--";
      tableHeader.value[9].value =
        res.buyType === "ORDINARY"
          ? "普通单"
          : res.buyType === "PRIVATE_DOMAIN_GROUP_ORDER"
            ? "团购单"
            : "--";
      tableHeader.value[7].value =
        dayjs(res.signUpDeadline).format("YYYY-MM-DD HH:mm:ss") || "--";
      tableHeader.value[8].value = res.complex?.name || "--";
      tableHeader.value[6].value =
        dayjs(res.openTime).format("YYYY-MM-DD HH:mm:ss") || "--";
      if (res.leaders?.length) {
        let learderList = [];
        res.leaders.map(item => {
          learderList.push(item.name);
        });
        tableHeader.value[4].value = learderList.join(" 、") || "--";
      }
      if (res.lecturers?.length) {
        let lecturersList = [];
        res.lecturers.map(item => {
          lecturersList.push(item.name);
        });
        tableHeader.value[5].value = lecturersList.join(" 、") || "--";
      }
      tableHeader.value[10].value =
        COURSE_PERIOD_ENUM[res.coursePeriodState]?.label || "--";
      tableHeader.value[10].state = res.offlineType || "--";
      tableHeader.value[11].value = AUDIT_ENUM[res.reviewState]?.label || "无";
      tableHeader.value[11].opinion = res.opinion || "--";
      tableHeader.value[11].state = res.reviewState || "--";
      if (res.cover?.length) {
        res.cover.map(item => {
          srcList.value.push(item?.uploadFile?.url);
        });
        url.value = res.cover[0]?.uploadFile?.url;
      }
    } else if (route.query.text === "person") {
      // personHeader.value[0].value = res.name || "--";
      // personHeader.value[1].value = res.id || "--";
      // personHeader.value[2].value =
      //   dayjs(res.createdAt).format("YYYY-MM-DD HH:mm:ss") || "--";
      // personHeader.value[3].value =
      //   dayjs(res.openTime).format("YYYY-MM-DD HH:mm:ss") || "--";
      // personHeader.value[4].value = res.organization?.name || "--";
      // personHeader.value[5].value = res.complex?.name || "--";
      // personHeader.value[6].value =
      //   res.leaders?.map(it => it.name).join("、") || "--";
      // personHeader.value[7].value =
      //   res.lecturers?.map(it => it.name).join("、") || "--";
      // // personHeader.value[8].value = `¥ ${res.minPrice}` || "--";
      // // personHeader.value[9].value = "规格1、规格2";
      // // personHeader.value[10].value = '获取学分22' || "--";
      // // personHeader.value[11].value = '参与人数10 / 15' || "--";
    } else if (route.query.text === "work") {
      workHeader.value[7].value = res.name || "--";
      workHeader.value[8].value =
        dayjs(res.openTime).format("YYYY-MM-DD HH:mm:ss") || "--";
    }
  } else {
    console.log("🐳-----err-----", err);
  }
};
const studentIdNumberCt = ref();
// 查询学生详情
const getstudentFind = async () => {
  let [err, res] = await requestTo(
    studentFindById({ id: Number(route.query.studentId) })
  );
  // console.log("🐬-----res333-----", res);
  if (res) {
    workHeader.value[0].value = res.id || "--";
    workHeader.value[1].value = res.name || "--";
    workHeader.value[2].value =
      dayjs(res.createdAt).format("YYYY-MM-DD HH:mm:ss") || "--";
    workHeader.value[3].value = res.school || "--";
    workHeader.value[4].value = res.idType || "--";
    workHeader.value[5].value = res.idNumber || "--";
    workHeader.value[6].value =
      res.parentDTOS?.map(it => it.name).join("、") || "--";
    studentIdNumberCt.value = decrypt(res?.idNumberCt) || "--";
  } else {
    console.log("🐳-----err-----", err);
  }
  if (err) {
    ElMessage.error(err.msg);
  }
};
const iconteyp = ref(false);
// 家长的学生手机号
function imgAdd(val) {
  iconteyp.value = !iconteyp.value;
}

const sendContentData = ref({});
const contentData = val => {
  // console.log('💗evaluateData----33------>',val);
  if (val && val.showcontent) {
    contentShow.value = val.showcontent;
    sendContentData.value = val;
    sendContentData.value.name = tableHeader.value[0].value;
  }
};

const studentSituationFindByIdEVT = async () => {
  let [err, res] = await requestTo(
    studentSituationFindById({
      studentSituationId: route.query.studentSituationId
    })
  );
  if (res) {
    personHeader.value[0].value = res.coursePeriodName || "--";
    personHeader.value[1].value = res.id || "--";
    personHeader.value[2].value =
      dayjs(res.createdAt).format("YYYY-MM-DD HH:mm:ss") || "--";
    personHeader.value[3].value =
      dayjs(res.openTime).format("YYYY-MM-DD HH:mm:ss") || "--";
    personHeader.value[4].value = res.organizationName || "--";
    personHeader.value[5].value = res.complex?.name || "--";
    personHeader.value[6].value =
      res.leaders?.map(it => it.name).join("、") || "--";
    personHeader.value[7].value =
      res.lecturers?.map(it => it.name).join("、") || "--";
    personHeader.value[8].value = `¥ ${res.price || 0}`;
    personHeader.value[9].value = JSON.parse(res.specification).join(" 、 ");
    personHeader.value[10].value = res.credit || "5";
    personHeader.value[11].value = res.purchaseNumber || 0;
  } else {
    console.log("🐳-----err-----", err);
  }
  if (err) {
    // ElMessage.error(err.msg);
  }
};

onMounted(() => {
  if (route.query.text === "person") {
    studentSituationFindByIdEVT();
  } else {
    getCoursePeriodFind();
  }
  if (route.query.showTabContent) {
    showTabContent.value = route.query.showTabContent;
  }
  if (route.query.contentShow) {
    contentShow.value = route.query.contentShow;
  }
  if (route.query.studentId) {
    sendContentData.value = route.query;
    getstudentFind();
  }
});
</script>

<template>
  <div class="examine-detail">
    <!-- 新增 info 容器，用于控制可折叠区域 -->
    <div
      v-if="route.query.text === 'course'"
      class="info"
      :class="{ 'info-collapsed': isCollapsed }"
    >
      <!-- 原始布局，用于展开状态 -->
      <div v-show="!isCollapsed" class="curse-table">
        <div class="img">
          <el-tooltip
            class="box-item"
            title=""
            :content="coursePeriodName ? coursePeriodName : ''"
            placement="bottom"
            effect="light"
          >
            <div class="coursePeriodName">
              {{ coursePeriodName || "" }}
            </div>
          </el-tooltip>
          <el-image
            :src="ImageThumbnail(url, '115x')"
            :zoom-rate="1.2"
            :max-scale="7"
            :min-scale="0.2"
            :preview-src-list="srcList"
            :hide-on-click-modal="true"
            show-progress
            :initial-index="4"
            fit="cover"
            class="img-pic"
          />
        </div>
        <el-descriptions
          class="margin-top"
          title=""
          :column="4"
          border
          style="flex: 1; min-width: 0"
        >
          <template v-for="(item, index) in tableHeader" :key="index">
            <el-descriptions-item width="120px" label-align="center">
              <template #label>
                <div class="cell-item">{{ item.label }}</div>
              </template>
              <div
                v-if="
                  (item.label === '审核状态' &&
                    item.state === 'OFFLINE_REJECT') ||
                  item.state === 'ONLINE_REJECT'
                "
              >
                <el-tooltip
                  class="box-item"
                  title=""
                  :content="item.opinion ? item.opinion : '无'"
                  placement="bottom"
                  effect="light"
                >
                  <div class="warning">
                    {{ item.value }}
                    <el-icon style="color: red; margin-left: 3px">
                      <Warning />
                    </el-icon>
                  </div>
                </el-tooltip>
              </div>
              <div
                v-else-if="
                  (item.label === '课期状态' &&
                    item.state === 'PLATFORM_OFFLINE') ||
                  item.state === 'PLATFORM_CLOSE_GROUP'
                "
              >
                <el-tooltip
                  class="box-item"
                  title=""
                  :content="
                    item.state === 'PLATFORM_OFFLINE'
                      ? '课期已被强制下架，请联系平台客服了解情况'
                      : '课期已被强制关闭团购，请联系平台客服了解情况'
                  "
                  placement="bottom"
                  effect="light"
                >
                  <div class="warning">
                    {{ item.value }}
                    <el-icon style="color: red; margin-left: 3px">
                      <Warning />
                    </el-icon>
                  </div>
                </el-tooltip>
              </div>
              <div v-else>
                {{ item.value }}
              </div>
            </el-descriptions-item>
          </template>
        </el-descriptions>
      </div>

      <!-- 新增：收起状态的布局 -->
      <div v-show="isCollapsed" class="curse-table collapsed-table">
        <el-descriptions
          class="margin-top first-row-only"
          title=""
          :column="4"
          border
          style="width: 100%"
        >
          <template v-for="(item, index) in firstRowHeaders" :key="index">
            <el-descriptions-item width="120px" label-align="center">
              <template #label>
                <div class="cell-item">{{ item.label }}</div>
              </template>
              <!-- 收缩时简化显示，不包含复杂状态的tooltip和icon -->
              <div>{{ item.value }}</div>
            </el-descriptions-item>
          </template>
        </el-descriptions>
      </div>

      <!-- 新增：收起/展开控制按钮 -->
      <div class="collapse-bar" @click="toggleCollapse">
        <div class="collapse-control">
          <el-icon class="collapse-icon">
            <ArrowDown v-if="isCollapsed" />
            <ArrowUp v-else />
          </el-icon>
        </div>
      </div>
    </div>
    <div v-else-if="route.query.text === 'person'" class="curse-table1">
      <el-descriptions class="margin-top" title="" :column="4" border>
        <template v-for="(item, index) in personHeader" :key="index">
          <el-descriptions-item width="120px" label-align="center">
            <template #label>
              <div class="cell-item">{{ item.label }}</div>
            </template>
            {{ item.value }}
          </el-descriptions-item>
        </template>
      </el-descriptions>
    </div>
    <div v-else-if="route.query.text === 'work'" class="curse-table1">
      <el-descriptions class="margin-top" title="" :column="3" border>
        <template v-for="(item, index) in workHeader" :key="index">
          <el-descriptions-item width="120px" label-align="center">
            <template #label>
              <div class="cell-item">{{ item.label }}</div>
            </template>
            <div v-if="item.prop === 'idNumber'" style="width: 180px">
              <span v-if="!iconteyp" style="margin-right: 10px">
                {{ item.value }}
              </span>
              <span v-else style="margin-right: 10px">
                {{ studentIdNumberCt }}
              </span>
              <el-icon v-if="!iconteyp" style="cursor: pointer" @click="imgAdd">
                <Hide />
              </el-icon>
              <el-icon v-else style="cursor: pointer" @click="imgAdd">
                <View />
              </el-icon>
            </div>
            <div v-else>
              {{ item.value }}
            </div>
          </el-descriptions-item>
        </template>
      </el-descriptions>
    </div>
    <div v-if="showTabContent === true" class="info-table">
      <!-- tab切换 -->
      <TabTitle
        :tabTitle="tabTitle"
        :targetTab="targetTab"
        @tab-data="tabInfoEvt"
      />
      <!-- 切换信息 -->
      <div class="tab-info">
        <Scheduling
          v-if="infoShow === '行程安排'"
          :periodId="Number(route.query.periodId)"
        />
        <CourseReport v-else-if="infoShow === '课期报告'" />
        <PriceSetting v-else-if="infoShow === '价格设置'" />
        <courseIntroduction
          v-else-if="
            infoShow === '课期介绍' ||
            infoShow === '课期知识点' ||
            infoShow === '装备说明' ||
            infoShow === '注意事项' ||
            infoShow === '用户协议'
          "
          :periodId="Number(route.query.periodId)"
          :tableTitle="infoShow"
        />
        <StudentSituation
          v-else-if="infoShow === '学生情况'"
          :maxPeopleNumber="coursePeroidInfo.maxPeopleNumber"
          :purchaseNumber="coursePeroidInfo.purchaseNumber"
          @tab-show="evaluateTabShow"
          @homework="contentData"
          @related-order="contentData"
        />
        <JobDesign v-else-if="infoShow === '作业设计'" />
        <UserEvaluate
          v-else-if="infoShow === '用户评价'"
          :periodName="tableHeader[0].value"
          @tab-show="evaluateTabShow"
          @evaluate="contentData"
        />
        <ClassTrack v-else-if="infoShow === '课堂跟踪'" />
      </div>
    </div>
    <div v-else class="info-detail">
      <EvaluateContent
        v-if="contentShow === 'evaluate'"
        :evaluateData="sendContentData"
        @tab-show="evaluateTabShow"
        @tab-text="tabText"
      />
      <HomeworkContent
        v-else-if="contentShow === 'homework'"
        :homeworkData="sendContentData"
        @tab-show="evaluateTabShow"
      />
      <order
        v-else-if="contentShow === 'order'"
        :orderData="sendContentData"
        @tab-show="evaluateTabShow"
      />
    </div>
  </div>
</template>

<style lang="scss" scoped>
.examine-detail {
  display: flex;
  flex-direction: column;
  height: calc(100vh - 100px);
  .curse-table {
    box-sizing: border-box;
    display: flex;
    justify-content: space-between;
    width: 100%;
    padding: 20px 20px 0 20px;
    // height: 250px;
    // margin-bottom: 20px;
    // width: calc(100% - 48px);
    background-color: #fff;
    .img {
      width: 115px;
      display: flex;
      flex-direction: column;
      align-items: center;
      margin-right: 20px;
      .coursePeriodName {
        width: 100%;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        margin-bottom: 10px;
      }
      .img-pic {
        width: 115px;
        height: 88px;
        // height: 100%;
        // margin-left: 20px;
        // object-fit: cover;
      }
    }
  }
  .curse-table1 {
    box-sizing: border-box;
    // display: flex;
    // justify-content: space-between;
    width: 100%;
    padding: 24px 40px;
    // height: 250px;
    margin-bottom: 30px;
    // width: calc(100% - 48px);
    background-color: #fff;
  }

  .info-table {
    width: 100%;
    flex: 1;
    overflow: hidden;
    // background-color: #fff;
    display: flex;
    flex-direction: column;
    .tab-info {
      box-sizing: border-box;
      width: 100%;
      flex: 1;
      overflow: auto;
      padding: 20px;
      background-color: #fff;
    }
  }
  .info-detail {
    width: 100%;
    flex: 1;
    overflow: auto;
    background-color: #fff;
    padding: 20px;
  }
}

:deep(.el-descriptions__label.el-descriptions__cell.is-bordered-label) {
  background: #e1f5ff;
}
.warning {
  display: flex;
  align-items: center;
}

.info {
  background-color: #fff;
  box-sizing: border-box;
  // padding: 20px 20px 0 20px;
  margin-bottom: 20px;
  transition: all 0.3s ease;
  overflow: hidden;
  flex-shrink: 0;

  &.info-collapsed {
    // padding-top: 20px;
    padding-bottom: 0;
  }

  // .curse-table {
  //   &.collapsed-table {
  //   }
  // }
}

.collapse-bar {
  height: 30px;
  // border-radius: 0 0 4px 4px;
  width: calc(100% + 40px);
  cursor: pointer;
  display: flex;
  justify-content: center;
  align-items: center;
  // box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  position: relative;
  right: 20px;
  z-index: 1;

  &:hover {
    background-color: #ecf5ff;
  }

  .collapse-control {
    display: flex;
    justify-content: center;
    align-items: center;

    .collapse-icon {
      color: #909399;
      font-size: 16px;
      transition: transform 0.3s;

      &:hover {
        color: #409eff;
      }
    }
  }
}
</style>
