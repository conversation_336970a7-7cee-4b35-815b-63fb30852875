import { http } from "@/utils/http";

/** 新闻新增 */
export const newsSave = (data, operateLog = {}) => {
  return http.request(
    "post",
    "/platform/news/save",
    { data },
    {
      isNeedToken: true,
      isNeedEncrypt: true,
      operateLog: {
        operateLogType: operateLog?.operateLogType || "PLATFORM_SETTINGS",
        additionalParameter: operateLog?.additionalParameter || "",
        operatorTarget: operateLog?.operatorTarget || "",
        operateType: operateLog?.operateType || ""
      }
    }
  );
};

/** 分页查询 */
export const newsFindAll = params => {
  return http.request(
    "get",
    "/platform/news/findAll",
    { params },
    { isNeedToken: true, isNeedEncrypt: true }
  );
};

/** 根据Id查询详情 */
export const newsFindById = params => {
  return http.request(
    "get",
    "/platform/news/findById",
    { params },
    { isNeedToken: true, isNeedEncrypt: true }
  );
};

/** 删除 */
export const newsDelete = (data, operateLog = {}) => {
  return http.request(
    "post",
    "/platform/news/delete",
    { data },
    {
      isNeedToken: true,
      isNeedEncrypt: true,
      operateLog: {
        operateLogType: operateLog?.operateLogType || "PLATFORM_SETTINGS",
        additionalParameter: operateLog?.additionalParameter || "",
        operatorTarget: operateLog?.operatorTarget || "",
        operateType: operateLog?.operateType || ""
      }
    }
  );
};

/** 修改/编辑 */
export const newsUpdate = (data, operateLog = {}) => {
  return http.request(
    "post",
    "/platform/news/update",
    { data },
    {
      isNeedToken: true,
      isNeedEncrypt: true,
      operateLog: {
        operateLogType: operateLog?.operateLogType || "PLATFORM_SETTINGS",
        additionalParameter: operateLog?.additionalParameter || "",
        operatorTarget: operateLog?.operatorTarget || "",
        operateType: operateLog?.operateType || ""
      }
    }
  );
};
