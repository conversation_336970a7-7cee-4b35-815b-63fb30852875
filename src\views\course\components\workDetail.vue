<script setup>
import { useRouter, useRoute } from "vue-router";
import { onMounted, ref, computed, watch } from "vue";
import { formatTime } from "@/utils/index";
import {
  findByStudentAssignmentId,
  gradingWork,
  findStudentAssignment
} from "@/api/period.js";
import { requestTo } from "@/utils/http/tool";
import { to } from "@iceywu/utils";
import { ElMessage, ElMessageBox } from "element-plus";
import ImgList from "@/components/Base/list1.vue";
import DescriptionList from "./descriptionList.vue";
const router = useRouter();
const route = useRoute();
const detailShow = ref(false);
const deteilInfo = ref({
  textarea: "",
  score: "",
  workArea: "",
  name: "",
  files: []
});
const fileListAPi = ref([]);
const srcList = ref([]);
const srcId = ref(0);
const showPreview = ref(false);
// 获取数据（根据学生作业id查作业批改分数内容）
const getFindByStudentAssignmentId = async () => {
  const params = {
    studentAssignmentId: Number(route.query.homeworkId)
  };
  const [err, result] = await to(findByStudentAssignmentId(params));
  // console.log("🦄result----111---4444----------------------->", result);
  if (result?.code === 200) {
    deteilInfo.value.score = result?.data?.score || "";
    deteilInfo.value.textarea = result?.data?.content;
  } else {
    console.log("获取失败");
  }
};
// 获取数据(查询学生作业)
const getFindStudentAssignment = async () => {
  const params = {
    studentId: Number(route.query.studentId),
    coursePeriodId: Number(route.query.periodId)
  };
  const [err, result] = await to(findStudentAssignment(params));
  if (result?.code === 200) {
    // console.log('🌵result-------33----------------------->',result.data);

    deteilInfo.value.name = result?.data?.student?.name;
    deteilInfo.value.workArea = result?.data?.content;
    deteilInfo.value.files = result?.data?.files;
    if (result?.data?.files?.length) {
      result?.data?.files?.map(item => {
        fileListAPi.value.push(item?.uploadFile);
        srcList.value.push(item?.uploadFile?.url);
      });
    }
  } else {
    console.log("获取失败");
  }
};
const periodName = ref("");
const periodNameEvt = val => {
  periodName.value = val;
};
const submitLoading = ref(false);
// 批改作业
const saveEvt = async () => {
  if (submitLoading.value) return;
  // 校验
  const validTextarea = validateTextarea(deteilInfo.value.textarea);
  if (!validTextarea) {
    submitLoading.value = false;
    return;
  }

  submitLoading.value = true;
  if (!deteilInfo.value.score) {
    ElMessage.error("请输入分数");
    return;
  }
  if (deteilInfo.value.score && deteilInfo.value.score < 0) {
    ElMessage.error("分数不能为负数");
    return;
  }
  const params = {
    studentAssignmentId: Number(route.query.homeworkId),
    // content: deteilInfo.value.textarea,
    score: Number(deteilInfo.value.score)
  };

  if (deteilInfo.value.textarea) {
    params.content = deteilInfo.value.textarea;
  }
  // console.log("🦄params------------------------------>", params);
  // return;
  const operateLog = {
    operateLogType: "COURSE_MANAGEMENT",
    operateType: `批改了“${periodName.value}”课期中的“${deteilInfo.value.name}”学生的作业`
    // operatorTarget: form.value.name,
  };
  const [err, result] = await to(gradingWork(params));
  //   console.log("🦄result------------------------------>", result);
  if (result?.code === 200) {
    ElMessage.success("保存成功");
    detailShow.value = !detailShow.value;
    getFindByStudentAssignmentId();
  } else {
    ElMessage.error(`保存失败,${result?.msg}`);
  }
  submitLoading.value = false;
};
// 图片预览
const handleClick = id => {
  showPreview.value = true;
  srcId.value = id;
};
const textareaError = ref("");
// 批改提示
const validateTextarea = val => {
  if (val && val.length >= 500) {
    textareaError.value = "批改内容不能超过500字";
    return false;
  }
  textareaError.value = "";
  return true;
};

// 批改和取消
const ccancelCorrection = () => {
  detailShow.value = !detailShow.value;
};
watch(() => deteilInfo.value.textarea, validateTextarea);
// 批改提示
onMounted(() => {
  getFindByStudentAssignmentId();
  getFindStudentAssignment();
});
</script>

<template>
  <div>
    <DescriptionList
      :periodId="Number(route.query.periodId)"
      @name="periodNameEvt"
    />
    <div class="evaluate-detail">
      <div class="user-comments">
        <div class="evaluate">
          <div class="work-content">
            <div class="title">
              <div class="text">
                <div class="name">{{ "学生姓名" }}</div>
                <div class="number">
                  {{ deteilInfo?.name ? `${deteilInfo?.name}` : "--" }}
                </div>
              </div>
            </div>
            <!-- <el-input
              v-model="deteilInfo.workArea"
              type="textarea"
              resize="none"
              disabled
              class="area"
              :placeholder="
                deteilInfo?.workArea ? deteilInfo?.workArea : '暂无数据'
              "
            /> -->
            <div class="area">
              {{ deteilInfo.workArea || "暂无数据" }}
            </div>
          </div>
          <div class="pic">
            <div class="title">
              <div class="text">作业配图</div>
            </div>
            <ImgList
              v-if="fileListAPi?.length"
              :imgList="fileListAPi"
              :srcList="srcList"
              :itemCount="fileListAPi?.length"
              :height="'200'"
            />
            <!-- <div v-if="fileListAPi?.length" class="banner">
            <el-carousel
              :interval="4000"
              type="card"
              height="200px"
              :autoplay="false"
            >
              <el-carousel-item
                v-for="(item, index) in fileListAPi"
                :key="item"
              >
                <img
                  :src="item.url"
                  class="h-full"
                  @click="handleClick(index)"
                >
              </el-carousel-item>
            </el-carousel>
            <el-image-viewer
              v-if="showPreview"
              :url-list="srcList"
              show-progress
              :initial-index="srcId"
              @close="showPreview = false"
            />
          </div> -->
            <el-empty v-else style="height: 200px" description="暂无配图" />
          </div>
        </div>
      </div>
      <div class="replies">
        <div class="content">
          <div class="text-area">
            <div class="title">
              <div class="text">
                {{ "作业批改" }}
              </div>
              <div class="text">
                <span class="name">{{ `作业分数` }}</span>
                <span v-if="!detailShow">{{ deteilInfo?.score || 0 }}</span>
                <!-- <el-input
                  v-else
                  v-model="deteilInfo.score"
                  type="number"
                  placeholder="请输入分数"
                  clearable
                  style="width: 180px"
                /> -->
                <el-input-number
                  v-else
                  v-model="deteilInfo.score"
                  :min="1"
                  :max="10"
                  placeholder="请输入分数"
                  style="width: 180px"
                />
              </div>
            </div>
            <el-input
              v-model.trim="deteilInfo.textarea"
              type="textarea"
              resize="none"
              :maxlength="500"
              show-word-limit
              :placeholder="
                deteilInfo.textarea
                  ? deteilInfo.textarea
                  : detailShow
                    ? '请输入批改内容'
                    : '暂无数据'
              "
              :disabled="!detailShow"
              @input="validateTextarea"
            />
            <div
              v-if="textareaError"
              style="color: red; font-size: 12px; margin-top: 2px"
            >
              {{ textareaError }}
            </div>
          </div>
          <div class="buttons">
            <!-- <div class="create" @click="detailShow = !detailShow">
            {{ "批改作业" }}
          </div> -->
            <el-button
              v-if="detailShow === false"
              style="margin-bottom: 10px"
              @click="
                router.replace({
                  path: '/course/courseDetails/currentDetails',
                  query: {
                    infoShow: '学生情况',
                    periodId: route.query.periodId,
                    courseId: route.query.courseId
                  }
                })
              "
            >
              返回
            </el-button>
            <div v-if="detailShow === false" class="create_btn">
              <el-button type="primary" @click="ccancelCorrection">
                {{ "批改作业" }}
              </el-button>
            </div>
            <div v-if="detailShow" class="create_btn">
              <el-button
                type="primary"
                style="margin-bottom: 10px"
                :loading="submitLoading"
                @click="saveEvt"
              >
                {{ "保存" }}
              </el-button>
              <el-button class="quit" @click="ccancelCorrection">
                取消
              </el-button>
              <!-- <el-button
              @click="
                router.replace({
                  path: '/course/courseDetails/currentDetails',
                  query: {
                    infoShow: '学生情况',
                    periodId: route.query.periodId,
                    courseId: route.query.courseId
                  }
                })
              "
            >
              取消
            </el-button> -->
            </div>
          </div>
          <!-- <div v-if="detailShow" class="buttons"> -->
          <!-- <div
            class="cancel"
            @click="
              router.replace({
                path: '/course/courseDetails/currentDetails',
                query: {
                  infoShow: '学生情况',
                  periodId: route.query.periodId,
                  courseId: route.query.courseId
                }
              })
            "
          >
            取消
          </div> -->
          <!-- <el-button
              type="primary"
              style="margin-bottom: 10px"
              :loading="submitLoading"
              @click="saveEvt"
            >
              {{ "保存" }}
            </el-button>
            <el-button
              @click="
                router.replace({
                  path: '/course/courseDetails/currentDetails',
                  query: {
                    infoShow: '学生情况',
                    periodId: route.query.periodId,
                    courseId: route.query.courseId
                  }
                })
              "
            >
              取消
            </el-button> -->
          <!-- <div class="create" @click="saveEvt">
            {{ "保存" }}
          </div> -->
          <!-- </div> -->
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.evaluate-detail {
  font-size: 14px;
  background-color: #fff;
  width: 100%;
  height: 650px;
  padding: 20px 20px;
  .user-comments {
    width: 100%;
    margin-bottom: 45px;
    .title {
      width: 100%;
      display: flex;
      margin-bottom: 10px;
      .text {
        display: flex;
        white-space: nowrap;
      }

      .name {
        margin-right: 50px;
      }
    }
    .evaluate {
      width: 100%;
      display: flex;
      justify-content: space-between;

      .work-content {
        width: 49%;
        .area {
          width: 100%;
          height: 200px;
          white-space: pre-wrap;
          overflow-y: auto;
          color: #b1b1b1;
          border: 1px solid #e6e6e6;
          padding: 10px;
        }
      }
      :deep(.el-textarea__inner) {
        height: 200px;
      }
      .pic {
        width: 49%;
        height: 160px;
        :deep(.el-carousel--horizontal) {
          height: 160px;
        }
      }
    }
  }
  .replies {
    width: 100%;
    .title {
      width: 100%;
      display: flex;
      justify-content: space-between;
      margin-bottom: 10px;
    }
    .content {
      width: 100%;
      display: flex;
      // justify-content:space-around;
      .text-area {
        width: 90%;
        margin-right: 50px;
        :deep(.el-textarea__inner) {
          height: 150px;
        }
        .title {
          width: 100%;
          display: flex;
          justify-content: space-between;
          .text {
            display: flex;
          }
          .name {
            margin-right: 18px;
            white-space: nowrap;
          }
        }
      }
      .buttons {
        // display: flex;
        // justify-content: space-between;
        // width: 95%;
        // margin: 0 auto;
        // margin-top: 28vh;

        display: flex;
        flex-direction: column;
        // align-items: center;
        justify-content: flex-end;

        .create_btn {
          display: flex;
          align-items: center;
          justify-content: center;
          flex-wrap: wrap;
          .quit {
            margin-right: 12px;
          }
        }

        .cancel {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 100px;
          height: 36px;
          color: rgb(255 255 255 / 100%);
          cursor: pointer;
          background-color: rgb(230 152 58 / 100%);
          border-radius: 6px;
          margin-bottom: 20px;
        }

        .create {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 100px;
          height: 36px;
          color: rgb(255 255 255 / 100%);
          cursor: pointer;
          background-color: rgb(64 149 229 / 100%);
          border-radius: 6px;
        }
      }
    }
  }
  // .el-carousel__item h3 {
  //   margin: 0;
  //   // line-height: 200px;
  //   color: #475669;
  //   text-align: center;
  //   opacity: 0.75;
  // }

  // .el-carousel__item:nth-child(2n) {
  //   background-color: #99a9bf;
  // }

  // .el-carousel__item:nth-child(2n + 1) {
  //   background-color: #d3dce6;
  // }
  :deep(.el-carousel__item) {
    margin: 0;
    line-height: 200px;
    text-align: center;
  }
}
.return_page {
  margin-bottom: 10px;
}
</style>
