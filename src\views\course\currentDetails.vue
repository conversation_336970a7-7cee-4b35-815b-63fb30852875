<script setup>
import {
  ref,
  onMounted,
  provide,
  onActivated,
  nextTick,
  watch,
  onBeforeUnmount,
  computed
} from "vue";
import { useRoute, useRouter } from "vue-router";
import {
  coursePeriodFind,
  coursePeriodOffline,
  coursePeriodOnline,
  periodOpenGroupOrder,
  coursePeriodDelete
} from "@/api/course.js";
import { usePopup } from "vue-hooks-pure";
import openDialog from "@/components/Base/openDialog.vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { requestTo } from "@/utils/http/tool";
import dayjs from "dayjs";
import OrderDialog from "@/components/Base/orderDialog.vue";
import TableList from "./components/tableList.vue";
import { courseStore } from "@/store/modules/course.js";
import { to } from "@iceywu/utils";
import { COURSE_PERIOD_ENUM, AUDIT_ENUM } from "@/utils/enum.js";
import { Warning, ArrowDown, ArrowUp } from "@element-plus/icons-vue";

defineOptions({
  name: "CurrentDetails"
});

onActivated(() => {
  getCoursePeriodFind();
});

const useCourseStore = courseStore();
const route = useRoute();
const router = useRouter();
const textarea = ref("");
const tableShow = ref(true);
const coursePeriodInfo = ref({});
// 是否收起内容
const isCollapsed = ref(false);
const formContainerRef = ref();
const formHeight = ref(0);

// 切换收起/展开状态
const toggleCollapse = () => {
  isCollapsed.value = !isCollapsed.value;
};

// 表头
const tableHeader = ref([
  {
    id: "1",
    label: "课期名",
    value: "",
    width: "107px"
  },
  {
    id: "2",
    label: "期号",
    value: "",
    width: "107px"
  },
  {
    id: "3",
    label: "课期ID",
    value: "",
    width: "107px"
  },
  {
    id: "4",
    label: "创建时间",
    value: "",
    width: "107px"
  },
  {
    id: "5",
    label: "领队",
    value: "",
    width: "107px"
  },
  {
    id: "6",
    label: "讲师",
    value: "",
    width: "107px"
  },
  {
    id: "7",
    label: "开课时间",
    value: "",
    width: "107px"
  },
  {
    id: "8",
    label: "报名截止",
    value: "",
    width: "107px"
  },
  {
    id: "9",
    label: "基地",
    value: "",
    width: "107px"
  },
  {
    id: "10",
    label: "购买类型",
    value: "",
    width: "107px"
  },
  {
    id: "11",
    label: "课期状态",
    value: "",
    width: "107px",
    state: ""
  },
  {
    id: "12",
    label: "审核状态",
    value: "",
    width: "107px",
    state: "",
    opinion: ""
  }
]);

// 将表头分为两部分：第一行和其余行，用于新视图
const firstRowHeaders = computed(() => tableHeader.value.slice(0, 4));

const url = ref();
const srcList = ref([]);

// 编辑基本信息
const editeInfo = () => {
  router.push({
    path: "/course/coursePeriodEdite",
    query: {
      periodId: route.query.periodId,
      courseId: route.query.courseId,
      fromPage: "currentDetail",
      type: "edite"
    }
  });
};
// 团购弹窗
const dialogFormVisible = ref(false);
// 开启团购
const openOrder = () => {
  dialogFormVisible.value = true;
  // usePopup(openDialog, {
  //   title: "开启团购确认",
  //   api: periodOpenGroupOrder,
  //   id: route.query.periodId,
  //   password: "",
  //   showContent: "groupOrder",
  //   rightBtnText: "确认开启"
  // });
};
// 团购分享
const orderShare = () => {
  router.push({
    path: "/course/currentDetails/groupOrder",
    query: { periodId: route.query.periodId }
  });
};
// 是否上下架
const removeEvt = (row, bool) => {
  let freezeText =
    bool === true ? "确定要申请下架该课期吗？" : "确定要申请上架该课期吗？";
  let title = bool === true ? "下架申请" : "上架申请";
  ElMessageBox.confirm(`${freezeText}`, `${title}`, {
    confirmButtonText: "确定",
    cancelButtonText: "取消"
  })
    .then(() => {
      isFreezeApi(row, bool);
    })
    .catch(() => {});
};
const operateLog = ref({});
const isFreezeApi = async (row, bool) => {
  const params = {
    id: route.query.periodId
  };
  let api = coursePeriodOnline;
  operateLog.value = {
    operateLogType: "COURSE_MANAGEMENT",
    operateType: `上架了“${tableHeader.value[0].value}”课期`
    // operatorTarget: form.value.name,
  };
  if (bool) {
    api = coursePeriodOffline;
    operateLog.value = {
      operateLogType: "COURSE_MANAGEMENT",
      operateType: `下架了“${tableHeader.value[0].value}”课期`
      // operatorTarget: form.value.name,
    };
  }

  const { code, msg } = await api(params, operateLog.value);
  if (code === 200) {
    ElMessage({
      type: "success",
      message: bool === true ? "下架申请成功" : "上架申请成功"
    });
    getCoursePeriodFind();
  } else {
    if (code === 30039) {
      ElMessage({
        type: "error",
        message:
          bool === true
            ? `下架申请失败，${msg}`
            : `上架申请失败，${msg}，请编辑`
      });
    } else if (code === 30040) {
      ElMessage({
        type: "error",
        message:
          bool === true
            ? `下架申请失败，${msg}`
            : `上架申请失败，${msg}，请检查`
      });
    } else if (code === 30034) {
      ElMessage({
        type: "error",
        message:
          bool === true
            ? `下架申请失败，开课时间已过期`
            : `上架申请失败，开课时间已过期`
      });
    } else {
      ElMessage({
        type: "error",
        message: bool === true ? `下架申请失败，${msg}` : `上架申请失败，${msg}`
      });
    }
  }
};
// 删除课期
const deletePeriod = () => {
  ElMessageBox.confirm(`确定要删除该课期吗？`, "删除课期", {
    confirmButtonText: "确定",
    cancelButtonText: "取消"
  })
    .then(async () => {
      const operateLog = {
        operateLogType: "COURSE_MANAGEMENT",
        operateType: `删除了“${tableHeader.value[0].value}”课期`
        // operatorTarget: form.value.name,
      };
      let [err, res] = await to(
        coursePeriodDelete({ id: route.query.periodId }, operateLog)
      );
      if (res.code === 200) {
        ElMessage.success("删除成功");
        router.replace({
          path: "/course/courseDetails",
          query: { id: route.query.courseId }
        });
      } else {
        ElMessage.error(`删除失败,${res.msg}`);
      }
      if (err) {
        ElMessage.error("删除失败");
      }
    })
    .catch(() => {});
};

// 是否展示切换页
const tableShowEvt = val => {
  tableShow.value = val;
};
const state = ref();
const freezeCourse = ref(false);
// 查询课期详情
const getCoursePeriodFind = async () => {
  let [err, res] = await requestTo(
    coursePeriodFind({ id: route.query.periodId })
  );
  if (res) {
    // console.log("🐬-----res1111--333---", res);
    coursePeriodInfo.value = res || {};
    freezeCourse.value = res.courseIsFreeze || false;
    tableHeader.value[0].value = res.name || "--";
    tableHeader.value[1].value = res.termNumber || "0";
    tableHeader.value[2].value = res.id || "--";
    tableHeader.value[3].value =
      dayjs(res.createdAt).format("YYYY-MM-DD HH:mm:ss") || "--";
    tableHeader.value[9].value =
      res.buyType === "ORDINARY"
        ? "普通单"
        : res.buyType === "PRIVATE_DOMAIN_GROUP_ORDER"
          ? "团购单"
          : "--";
    // tableHeader.value[5].value = res.organization?.name || "--";
    tableHeader.value[8].value = res.complex?.name || "--";
    tableHeader.value[6].value =
      dayjs(res.openTime).format("YYYY-MM-DD HH:mm:ss") || "--";
    tableHeader.value[7].value =
      dayjs(res.signUpDeadline).format("YYYY-MM-DD HH:mm:ss") || "--";
    if (res.leaders?.length) {
      let learderList = [];
      res.leaders.map(item => {
        learderList.push(item.name);
      });
      tableHeader.value[4].value = learderList.join(" 、") || "--";
    }
    if (res.lecturers?.length) {
      let lecturersList = [];
      res.lecturers.map(item => {
        lecturersList.push(item.name);
      });
      tableHeader.value[5].value = lecturersList.join(" 、") || "--";
    }
    tableHeader.value[10].value =
      COURSE_PERIOD_ENUM[res.coursePeriodState]?.label || "--";
    tableHeader.value[10].state = res.offlineType || "--";
    tableHeader.value[11].value = AUDIT_ENUM[res.reviewState]?.label || "无";
    tableHeader.value[11].state = res.reviewState || "NONE";
    tableHeader.value[11].opinion = res.opinion || "无";
    state.value = res.coursePeriodState;
    useCourseStore.savePeriodState(state.value);
    if (res.cover?.length) {
      res.cover.map(item => {
        srcList.value.push(item?.uploadFile?.url);
      });
      url.value = res.cover[0]?.uploadFile?.url;
    }
  } else {
    console.log("🐳-----err-----", err);
  }
};

onMounted(() => {
  getCoursePeriodFind();
});
</script>

<template>
  <div class="examine-detail">
    <div
      ref="formContainerRef"
      class="info"
      :class="{ 'info-collapsed': isCollapsed }"
    >
      <div
        v-if="
          route.path !== '/course/currentDetails/groupOrder' &&
          route.query.related !== 'otherModle'
        "
        class="con_top"
      >
        <el-button
          v-if="state === 'OFFLINE' || state === 'NOT_LISTED'"
          type="danger"
          @click="deletePeriod"
        >
          删除课期
        </el-button>
        <el-button
          v-if="state === 'ONLINE' && tableHeader[9].value === 'ORDINARY'"
          type="primary"
          @click="removeEvt(row, true)"
        >
          下架
        </el-button>
        <el-button
          v-else-if="state === 'OFFLINE' || state === 'NOT_LISTED'"
          type="primary"
          @click="removeEvt(row, false)"
        >
          上架
        </el-button>
        <el-button
          v-if="state === 'OFFLINE' || state === 'NOT_LISTED'"
          type="primary"
          @click="openOrder"
        >
          开启团购
        </el-button>
        <el-button
          v-if="
            tableHeader[9].value === 'PRIVATE_DOMAIN_GROUP_ORDER' &&
            state === 'ONLINE'
          "
          type="primary"
          @click="orderShare"
        >
          团购分享
        </el-button>
        <el-button
          v-if="
            freezeCourse === false &&
            (state === 'OFFLINE' || state === 'NOT_LISTED')
          "
          type="primary"
          @click="editeInfo"
        >
          编辑课期
        </el-button>
      </div>

      <!-- 完整的表格(展开状态) -->
      <div v-show="!isCollapsed" class="curse-table">
        <el-descriptions
          title=""
          :column="4"
          border
          class="descriptions-table full-descriptions"
        >
          <template v-for="(item, index) in tableHeader" :key="index">
            <el-descriptions-item width="120px" label-align="center">
              <template #label>
                <div class="cell-item">{{ item.label }}</div>
              </template>
              <div
                v-if="
                  (item.label === '审核状态' &&
                    item.state === 'OFFLINE_REJECT') ||
                  item.state === 'ONLINE_REJECT'
                "
              >
                <el-tooltip
                  class="box-item"
                  title=""
                  :content="item.opinion ? item.opinion : '无'"
                  placement="bottom"
                  effect="light"
                >
                  <div class="warning">
                    {{ item.value }}
                    <el-icon style="color: red; margin-left: 3px">
                      <Warning />
                    </el-icon>
                  </div>
                </el-tooltip>
              </div>
              <div
                v-else-if="
                  (item.label === '课期状态' &&
                    item.state === 'PLATFORM_OFFLINE') ||
                  item.state === 'PLATFORM_CLOSE_GROUP'
                "
              >
                <el-tooltip
                  class="box-item"
                  title=""
                  :content="
                    item.state === 'PLATFORM_OFFLINE'
                      ? '课期已被强制下架，请联系平台客服了解情况'
                      : '课期已被强制关闭团购，请联系平台客服了解情况'
                  "
                  placement="bottom"
                  effect="light"
                >
                  <div class="warning">
                    {{ item.value }}
                    <el-icon style="color: red; margin-left: 3px">
                      <Warning />
                    </el-icon>
                  </div>
                </el-tooltip>
              </div>
              <div v-else>
                {{ item.value }}
              </div>
            </el-descriptions-item>
          </template>
        </el-descriptions>
        <div class="img">
          <!-- <img src="@/assets/user.jpg" alt="" /> -->
          <el-image
            :src="url"
            :zoom-rate="1.2"
            :max-scale="7"
            :min-scale="0.2"
            :preview-src-list="srcList"
            :hide-on-click-modal="true"
            show-progress
            :initial-index="0"
            fit="cover"
            class="img-pic"
          />
        </div>
      </div>
      <!-- 只有第一行的精简表格(收缩状态) -->
      <div v-show="isCollapsed" class="curse-table collapsed-table">
        <el-descriptions
          title=""
          :column="4"
          border
          class="descriptions-table first-row-only"
        >
          <template v-for="item in firstRowHeaders" :key="item.id">
            <el-descriptions-item width="120px" label-align="center">
              <template #label>
                <div class="cell-item">{{ item.label }}</div>
              </template>
              <div>{{ item.value }}</div>
            </el-descriptions-item>
          </template>
        </el-descriptions>
      </div>

      <!-- 伸缩控制箭头按钮 -->
      <div class="collapse-bar" @click="toggleCollapse">
        <div class="collapse-control">
          <el-icon class="collapse-icon">
            <ArrowDown v-if="isCollapsed" />
            <ArrowUp v-else />
          </el-icon>
        </div>
      </div>
    </div>

    <div class="info-table">
      <!-- <router-view /> -->
      <!-- hjhjh -->
      <TableList :maxPeopleNumber="coursePeriodInfo.maxPeopleNumber" />
    </div>
    <OrderDialog
      :id="Number(route.query.periodId)"
      v-model:dialogFormVisible="dialogFormVisible"
      :api="periodOpenGroupOrder"
      :operateLogType="'COURSE_MANAGEMENT'"
      :operateType="`开启了“${tableHeader[0].value}”课期的团购`"
      :logOut="false"
      :showContent="'groupOrder'"
      :textRightBtn="'确认开启'"
      :textLeftBtn="'取消'"
      :title="'开启团购确认'"
      @reset="dialogFormVisible = false"
    />
  </div>
</template>

<style lang="scss" scoped>
.examine-detail {
  display: flex;
  flex-direction: column;
  position: relative;
  // height: 85vh;
  height: calc(100vh - 100px);

  .info {
    background-color: #fff;
    box-sizing: border-box;
    padding: 10px 20px 0 20px;
    margin-bottom: 20px;
    transition: all 0.3s ease;
    opacity: 1;
    overflow: hidden;

    &.info-collapsed {
      padding-top: 10px;
      padding-bottom: 0;

      .con_top {
        display: none;
      }
    }

    .con_top {
      display: flex;
      justify-content: flex-end;
      margin-bottom: 10px;
      flex-wrap: wrap;
      // gap: 10px;
    }

    .curse-table {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      width: 100%;
      opacity: 1;
      transition: opacity 0.3s ease;

      .descriptions-table {
        width: calc(100% - 165px);
        flex-shrink: 0;
        transition: width 0.3s ease;
      }

      .img {
        width: 145px;
        height: 120px;
        margin-left: 20px;
        flex-shrink: 0;
        transition:
          opacity 0.3s ease,
          width 0.3s ease,
          margin-left 0.3s ease;

        .img-pic {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }
      }

      &.collapsed-table {
        // 收缩状态下的表格
        .descriptions-table {
          width: 100%; // 图片已隐藏，表格占据全宽
        }
      }

      .warning {
        display: flex;
        align-items: center;
      }
    }
  }

  .collapse-bar {
    width: calc(100% + 40px);
    height: 30px;
    background-color: #fff;
    // border-radius: 0 0 4px 4px;
    cursor: pointer;
    display: flex;
    justify-content: center;
    align-items: center;
    // margin-bottom: 20px;
    // box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    position: relative;
    right: 20px;
    z-index: 1;

    &:hover {
      background-color: #ecf5ff;
    }

    .collapse-control {
      display: flex;
      justify-content: center;
      align-items: center;

      .collapse-icon {
        color: #909399;
        font-size: 16px;
        transition: transform 0.3s;

        &:hover {
          color: #409eff;
        }
      }
    }
  }

  .info-table {
    width: 100%;
    flex: 1;
    flex-shrink: 0;
    overflow: hidden;
    // overflow: auto;
  }
}

:deep(.el-descriptions__label.el-descriptions__cell.is-bordered-label) {
  background: #e1f5ff;
}

// 调整首行表格视觉样式
:deep(.first-row-only) {
  .el-descriptions__body {
    // 可以添加特定样式
  }
}

// 使用 v-show 而不是 display:none 来获得更好的过渡动画
.curse-table {
  transition: opacity 0.3s ease;
}
</style>
